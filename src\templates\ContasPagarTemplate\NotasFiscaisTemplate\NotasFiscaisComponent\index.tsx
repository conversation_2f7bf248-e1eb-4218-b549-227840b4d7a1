import { Box, Button, CircularProgress, Tab, Tabs } from '@mui/material'
import { useEffect, useState } from 'react'
import { IDataFilterNotasFiscais } from '../Filtros/filtro-types'
import FitroNotasFiscais from '../Filtros/FIltroNotasFiscais'
import Aprovadas from './Aprovadas'
import Canceladas from './Canceladas'
import EmAnalise from './EmAnalise'
import Reprovadas from './Reprovadas'
import * as S from './styles'
import { NotaFiscal } from 'src/services/ContasPagar/nota-fiscal-controller'
import { IGetNFPaginable, IGetNFPaginableProps } from 'src/services/ContasPagar/nota-fiscal-controller/contas-type'
import { useToast } from 'hooks/toast'
import { EnumSituacaoNF, EnumTipoPrestador } from 'src/services/ContasPagar/prestador-controller/enuns'
import { useContext } from 'react'
import FilterNFContext from 'context/FilterNFcontext'

export const initialNotasFiscaisFilterValue: IDataFilterNotasFiscais = {
    ...FitroNotasFiscais,
    tipo: 'CREDENCIADO',
    maiorMenorValorBruto: 'ASC',
    situacao: 'EM_ANALISE'
}

const NotasFiscaisComponent = () => {
    const { addToast } = useToast()
    const { filterNF, setFilterNF } = useContext(FilterNFContext)
    // const [dataFilter, setDataFilter] = useState<IDataFilterNotasFiscais>(filterNF)
    const [notasFiscais, setNotasFiscais] = useState<IGetNFPaginable>()
    // const [value, setValue] = useState<'EM_ANALISE' | 'REPROVADA' | 'APROVADA' | 'CANCELADA'>(initialNotasFiscaisFilterValue.situacao)
    const [numberPage, setNumberPage] = useState<number>(0)
    const [loadingDados, setLoadingDados] = useState(true)

    // const handleChange = (event: React.SyntheticEvent, newValue: string) => {
    //     setValue(newValue)
    // }
    function loadDados(competencia?: string) {
        setLoadingDados(true)
        // const regexCnpj = /^\d{2}\.\d{3}\.\d{3}\/\d{4}-\d{2}$/
        // const isCnpj = regexCnpj.test(filterNF?.prestador)

        const props: IGetNFPaginableProps = {
            competencia: competencia ? competencia : filterNF?.competencia,
            situacao: EnumSituacaoNF[filterNF?.situacao],
            tipoPrestador: EnumTipoPrestador[filterNF?.tipo],
            nomeCNPJCodigo: filterNF?.prestador,
            codigoIbgeMunicipio: filterNF?.municipio?.value,
            dataEnvioInicial: filterNF?.dataEnvio?.dataEnvioInicial,
            dataEnvioFinal: filterNF?.dataEnvio?.dataEnvioFinal,
            page: numberPage,
            sort: filterNF?.maiorMenorValorBruto ? [`valorNota,${filterNF?.maiorMenorValorBruto}`] : ['dataEnvio,ASC'],
            size: 10
        }

        NotaFiscal.get({
            ...props
        })
            .then(({ data }) => {
                setNotasFiscais(data)
                setLoadingDados(false)
            })
            .catch((err) => {
                addToast({
                    title: err?.response?.data?.message ? err?.response?.data?.message : 'Ocorreu um erro: Desconhecido',
                    type: 'error',
                    duration: 5000
                })
            })
    }

    const handleLimparFiltros = () => {
        const competenciaAtual = filterNF?.competencia
        const filtrosLimpos = {
            ...initialNotasFiscaisFilterValue,
            competencia: competenciaAtual
        }

        setFilterNF(filtrosLimpos)
        setNumberPage(0)
        setNotasFiscais(undefined)

        // Limpar sessionStorage
        try {
            const keys = ['@filters:/notas-fiscais:notas-fiscais', '@filters:/notas-fiscais:filter-nf']
            keys.forEach((key) => sessionStorage.removeItem(key))
        } catch (error) {
            console.error('Erro ao limpar sessionStorage:', error)
        }

        // Recarregar dados com filtros limpos
        const props: IGetNFPaginableProps = {
            competencia: competenciaAtual || '',
            situacao: EnumSituacaoNF[initialNotasFiscaisFilterValue.situacao],
            tipoPrestador: EnumTipoPrestador[initialNotasFiscaisFilterValue.tipo],
            nomeCNPJCodigo: '',
            codigoIbgeMunicipio: undefined,
            dataEnvioInicial: undefined,
            dataEnvioFinal: undefined,
            page: 0,
            sort: [`valorNota,${initialNotasFiscaisFilterValue.maiorMenorValorBruto}`],
            size: 10
        }

        NotaFiscal.get(props)
            .then(({ data }) => {
                setNotasFiscais(data)
            })
            .catch((err) => {
                addToast({
                    title: err?.response?.data?.message ? err?.response?.data?.message : 'Ocorreu um erro: Desconhecido',
                    type: 'error',
                    duration: 5000
                })
            })
    }

    useEffect(() => {
        if (filterNF?.competencia) {
            loadDados()
        }
    }, [numberPage, filterNF?.situacao])

    return (
        <>
            <S.WrapperFilter>
                <FitroNotasFiscais
                    dataFilter={filterNF}
                    setDataFilter={setFilterNF}
                    buscar={(e) => {
                        setNumberPage(0)
                        loadDados(e)
                    }}
                />

                {filterNF &&
                    Object.keys(filterNF).length > 0 &&
                    (filterNF.competencia ||
                        filterNF.prestador ||
                        filterNF.municipio ||
                        filterNF.dataEnvio?.dataEnvioInicial ||
                        filterNF.dataEnvio?.dataEnvioFinal) && (
                        <div style={{ marginBottom: '16px', marginRight: '20px', textAlign: 'right' }}>
                            <Button variant="outlined" color="secondary" onClick={handleLimparFiltros}>
                                Limpar Filtros
                            </Button>
                        </div>
                    )}
            </S.WrapperFilter>

            {/* {loadingDados ? (
                <Box sx={{ display: 'flex', width: '100%', justifyContent: 'center', alignItems: 'center', height: '70vh' }}>
                    <CircularProgress />
                </Box>
            ) : ( */}
            <S.NotasFiscaisContainer>
                <Box sx={{ width: '100%', borderBottom: 1, borderColor: 'divider', background: '#FFF' }}>
                    <Tabs value={filterNF?.situacao} onChange={(e, newValue) => setFilterNF({ ...filterNF, situacao: newValue })}>
                        <Tab value="EM_ANALISE" label={<S.TabTitles>Em análise </S.TabTitles>} />
                        <Tab value="APROVADA" label={<S.TabTitles>Aprovadas </S.TabTitles>} />
                        <Tab value="REPROVADA" label={<S.TabTitles>Reprovadas </S.TabTitles>} />
                        <Tab value="CANCELADA" label={<S.TabTitles>Canceladas </S.TabTitles>} />
                    </Tabs>
                </Box>

                <>
                    {loadingDados ? (
                        <Box sx={{ display: 'flex', width: '100%', justifyContent: 'center', alignItems: 'center', height: '70vh' }}>
                            <CircularProgress />
                        </Box>
                    ) : (
                        <>
                            {filterNF?.situacao === 'EM_ANALISE' && (
                                <EmAnalise
                                    listagemNF={notasFiscais}
                                    loadingDados={loadingDados}
                                    setNumberPage={setNumberPage}
                                    setDataFilter={setFilterNF}
                                    dataFilter={filterNF}
                                />
                            )}

                            {filterNF?.situacao === 'APROVADA' && (
                                <Aprovadas
                                    listagemNF={notasFiscais}
                                    loadingDados={loadingDados}
                                    setNumberPage={setNumberPage}
                                    setDataFilter={setFilterNF}
                                    dataFilter={filterNF}
                                />
                            )}

                            {filterNF?.situacao === 'REPROVADA' && (
                                <Reprovadas
                                    listagemNF={notasFiscais}
                                    loadingDados={loadingDados}
                                    setNumberPage={setNumberPage}
                                    setDataFilter={setFilterNF}
                                    dataFilter={filterNF}
                                />
                            )}

                            {filterNF?.situacao === 'CANCELADA' && (
                                <Canceladas
                                    listagemNF={notasFiscais}
                                    loadingDados={loadingDados}
                                    setNumberPage={setNumberPage}
                                    setDataFilter={setFilterNF}
                                    dataFilter={filterNF}
                                />
                            )}
                        </>
                    )}
                </>
            </S.NotasFiscaisContainer>
            {/* )} */}
        </>
    )
}

export default NotasFiscaisComponent
