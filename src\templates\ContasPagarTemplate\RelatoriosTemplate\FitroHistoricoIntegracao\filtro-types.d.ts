import { EnumTipoPrestador } from 'src/services/ContasPagar/prestador-controller/enuns'
import { SituacaoPagamentoEnum } from 'src/types/enums'
import { ISortPage } from 'src/types/pagination'
import { TipoRelatorioEnum } from './enums'

export interface IDataFilterAcompanhamento {
    competencia?: {
        label: string
        value: string
    }
    prestadorUUID?: string
}

export interface IDataFilterRelatorio {
    competencia?: string
    prestadoresList?: Array<{ label: string; value: string }>
    situacaoPagamento?: SituacaoPagamentoEnum
    tipoPrestador?: {
        label: string
        value: string
    }
    tipoRelatorio?: TipoRelatorioEnum

    codigoIbgeMunicipio?: string
    dataInicialDemonstrativo?: string
    dataFinalDemonstrativo?: string
    dataInicialPagamento?: string
    dataFinalPagamento?: string
    dataInicialGeracaoLiq?: string
    dataFinalGeracaoLiq?: string
    dataAprovacaoInicial?: string
    dataAprovacaoFinal?: string
    separarMunicipios?: boolean
    municipioNome?: string
}

export interface IProducaoAprovadaProps extends ISortPage {
    competencia?: string
    dataFinalAprovacao?: string
    dataInicialAprovacao?: string
    tipoPrestador?: EnumTipoPrestador
    prestadoresIds?: string[]
}

export interface IRetencaoISSProps extends ISortPage {
    competencia: string
    prestadoresIds: string[]
    dataInicialPagamento: string
    dataFinalPagamento: string
    codigoIbgeMunicipio: string
    separarMunicipiosEmPaginas: boolean
}

export interface ILiquidacoesProps extends ISortPage {
    competencia: string
    prestadoresIds: string[]
    dataInicialPagamento: string
    dataFinalPagamento: string
    dataInicialGeracaoLiq: string
    dataFinalGeracaoLiq: string
}

export interface IAcompanhamentoPagamentoProps extends ISortPage {
    competencia?: string
    prestadoresIds?: string[]
    situacaoPagamento?: SituacaoPagamentoEnum
    dataInicialGeracaoDemonstrativo?: string
    dataFinalGeracaoDemonstrativo?: string
}
export interface IPrestadorBuscaProps {
    nomeCNPJ: string
    tipoRelatorio: string
}

