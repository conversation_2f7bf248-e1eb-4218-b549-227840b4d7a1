import { FormControl, InputLabel, MenuItem, Select, TextField } from '@mui/material'
import BeneficiarySearch from 'components/molecules/BeneficiarySearch'
import PrestadorInput from 'components/molecules/PrestadorInput/prestadorInput'
import Selectable from 'components/molecules/Select'
import { Dispatch, SetStateAction, useEffect, useState } from 'react'
import { ReactSVG } from 'react-svg'
import { apiCredenciamento } from 'src/services/api/api_credenciamento'
import { IPrestadorDTO } from 'src/types/Prestador-Beneficiario/prestador-beneficario'
import { IDataConsultarNF } from '../filtro-types'

import { selectCompetenciarOptions, selectTipoPrestadorOptions } from './mocksSelec'
import * as S from './styles'
import { parseDataToMMYYYY } from 'utils/functions'
import { Competencia } from 'src/services/ContasPagar/competencia-controller'
import AsyncSimpleSelect from 'components/atoms/AsyncSimpleSelect'
import { AsyncSelectOptionsTypes } from 'src/templates/ContasPagarTemplate/RelatoriosTemplate/FitroHistoricoIntegracao'
import { Relatorios } from 'src/services/ContasPagar/relatorio-controller'
import { selectValorOrderOptions } from 'src/templates/ContasPagarTemplate/PEDTemplate/Filtros/FIltroRetornoPED/mocksSelec'

type FitroConsultaNotasFiscais = {
    dataFilter: IDataConsultarNF
    setDataFilter: Dispatch<SetStateAction<IDataConsultarNF>>
    buscar: (data?: string) => void
}

type typeOptions = {
    label: string
    value: string
}

const FitroConsultaNotasFiscais = ({ dataFilter, setDataFilter, buscar }: FitroConsultaNotasFiscais) => {
    const [competenciaOptions, setCompetenciaOptions] = useState<typeOptions[]>([])
    const [loadingFilter, setLoadingFilter] = useState(true)

    function getCompetencias() {
        Competencia.get().then(({ data }) => {
            const competenciaOptions: typeOptions[] = data?.map((item) => ({
                label: parseDataToMMYYYY(item?.competencia?.toString(), 'MM/YYYY'),
                value: item?.competencia?.toString()
            }))

            setCompetenciaOptions(competenciaOptions)
            setDataFilter({
                ...dataFilter,
                competencia: dataFilter?.competencia
                    ? dataFilter?.competencia
                    : data?.[data?.length > 0 ? data?.length - 1 : 0]?.competencia?.toString()
            })
            buscar(dataFilter?.competencia ? dataFilter?.competencia : data?.[data?.length > 0 ? data?.length - 1 : 0]?.competencia?.toString())
            setLoadingFilter(false)
        })
    }

    const loadOptionsMunicipio = async (str: string): Promise<AsyncSelectOptionsTypes[]> => {
        return (
            str &&
            Relatorios.getMunicipio(str).then(({ data }) => {
                return parserMunicipio(data)
            })
        )
    }

    const parserMunicipio = (props): AsyncSelectOptionsTypes[] => {
        return props.map((item) => ({
            value: item?.codigoIbge,
            label: item?.municipio
        }))
    }

    useEffect(() => {
        getCompetencias()
    }, [])

    useEffect(() => {
        console.log(dataFilter)
    }, [dataFilter])

    return (
        <>
            {loadingFilter ? (
                <></>
            ) : (
                <S.BodyContainer>
                    <S.FilterRow0>
                        <FormControl>
                            <InputLabel id="competencia" required>
                                Competência
                            </InputLabel>
                            <Select
                                value={dataFilter?.competencia || ''}
                                onChange={(e) => {
                                    setDataFilter({
                                        ...dataFilter,
                                        competencia: e?.target?.value
                                    })
                                }}
                            >
                                {competenciaOptions?.map((item, index) => {
                                    return (
                                        <MenuItem key={index} value={item?.value}>
                                            {item?.label}
                                        </MenuItem>
                                    )
                                })}
                            </Select>
                        </FormControl>

                        <FormControl>
                            {/* <InputLabel>Município</InputLabel> */}
                            {/* <Select
                    defaultValue={dataFilter?.municipio}
                    value={dataFilter?.competencia}

                    onChange={(e) => {
                        setDataFilter({
                            ...dataFilter,
                            municipio: e?.target?.value
                        })
                    }}
                >
                    {selectTipoPrestadorOptions?.map((item, index) => {
                        return (
                            <MenuItem key={index} value={item?.value}>
                                {item?.label}
                            </MenuItem>
                        )
                    })}
                    <MenuItem>Sem Opções</MenuItem>
                </Select> */}
                            <AsyncSimpleSelect
                                label="Municipio"
                                value={dataFilter?.municipio}
                                onChange={(e) => {
                                    setDataFilter({
                                        ...dataFilter,
                                        municipio: e
                                    })
                                }}
                                isClearable
                                loadOptions={loadOptionsMunicipio}
                            />{' '}
                        </FormControl>
                        <S.SearchWrapper>
                            {/* <PrestadorInput handleOnChange={(e) => setDataFilter({ ...dataFilter, prestadorUUID: e?.uuid })} /> */}
                            <TextField
                                label="Prestador"
                                fullWidth
                                value={dataFilter?.prestador || ''}
                                onChange={({ target: { value } }) => setDataFilter({ ...dataFilter, prestador: value })}
                            />
                            <S.IconSearchWrapper onClick={() => buscar()}>
                                <ReactSVG src="/contas/assets/imgs/mai-ic-search.svg" />
                            </S.IconSearchWrapper>
                        </S.SearchWrapper>
                    </S.FilterRow0>
                    <S.FilterRow1>
                        <FormControl>
                            <InputLabel>Valor Bruto</InputLabel>
                            <Select
                                key={dataFilter?.dataEnvio as any}
                                displayEmpty
                                value={dataFilter?.maiorMenorValorBruto || ''}
                                onChange={(e) => {
                                    setDataFilter({
                                        ...dataFilter,
                                        maiorMenorValorBruto: e?.target?.value as 'ASC' | 'DESC',
                                        dataEnvio: null
                                    })
                                }}
                            >
                                <MenuItem value={null}></MenuItem>
                                {selectValorOrderOptions?.map((item, index) => {
                                    return (
                                        <MenuItem key={index} value={item?.value}>
                                            {item?.label}
                                        </MenuItem>
                                    )
                                })}
                            </Select>
                        </FormControl>
                        <FormControl>
                            <TextField
                                key={dataFilter?.maiorMenorValorBruto}
                                fullWidth
                                label="Data cadastro inicial"
                                type="date"
                                value={dataFilter?.dataEnvio?.dataEnvioInicial || ''}
                                onChange={(e) => {
                                    setDataFilter({
                                        ...dataFilter,
                                        dataEnvio: { ...dataFilter?.dataEnvio, dataEnvioInicial: e.target.value },
                                        maiorMenorValorBruto: null
                                    })
                                }}
                            />
                        </FormControl>
                        <FormControl>
                            <TextField
                                key={dataFilter?.maiorMenorValorBruto}
                                fullWidth
                                label="Data cadastro final"
                                type="date"
                                value={dataFilter?.dataEnvio?.dataEnvioFinal || ''}
                                onChange={(e) => {
                                    setDataFilter({
                                        ...dataFilter,
                                        dataEnvio: { ...dataFilter?.dataEnvio, dataEnvioFinal: e.target.value },
                                        maiorMenorValorBruto: null
                                    })
                                }}
                            />
                        </FormControl>
                    </S.FilterRow1>
                </S.BodyContainer>
            )}
        </>
    )
}

export default FitroConsultaNotasFiscais
