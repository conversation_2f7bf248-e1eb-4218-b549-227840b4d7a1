import { Box, Button, CircularProgress } from '@mui/material'
import NoItensComponent from 'components/atoms/NoItensComponent'
import PaginationNew from 'components/atoms/PaginationNew'
import SectionContainer from 'components/atoms/SectionContainer'
import TableResults from 'components/atoms/TableResults'
import { useFilterPersistence } from 'hooks/storage/useFilterPersistence'
import { useToast } from 'hooks/toast'
import { useEffect, useState } from 'react'
import { HistoricoIntegracao } from 'src/services/ContasPagar/historico-integracao-controller'
import { StatusArquivoEnum, StatusRetornoArquivoEnum, TipoArquivoEnum } from 'src/services/ContasPagar/historico-integracao-controller/enuns'
import { IGetHistoricoIntegracao } from 'src/services/ContasPagar/historico-integracao-controller/types'
import { IPagination } from 'src/types/pagination'
import { downloadFileV2, fileNameFromBlob } from 'utils/functions'
import { PaginationHelper } from 'utils/pagination-helper'
import { IDataFilterHistoricoIntegracao } from '../Filtros/filtro-types'
import FitroHistoricoIntegracao from '../Filtros/FitroHistoricoIntegracao'
import ExportarHistoricoModal from '../modais/ModalExportarHistorico'
import ItemList from './ItemList'
import * as S from './styles'

const initialRetornoPEDFilterValue: IDataFilterHistoricoIntegracao = {
    ...FitroHistoricoIntegracao
}

interface IPersistedFilters extends IDataFilterHistoricoIntegracao {
    numberPage: number
}

const HistoricoIntegracaoComponent = () => {
    const { addToast } = useToast()

    const {
        filters: persistedFilters,
        updateFilters: updatePersistedFilters,
        clearFilters: clearPersistedFilters
    } = useFilterPersistence({
        route: '/acompanhamento',
        filterType: 'historico-integracao',
        defaultValues: {
            ...initialRetornoPEDFilterValue,
            numberPage: 0
        }
    })

    const typedPersistedFilters = persistedFilters as IPersistedFilters | null

    const [loadingDados, setLoadingDados] = useState(false)
    const [pagination, setPagination] = useState<IPagination>()
    const [dataFilterHistorico, setDataFilterHistorico] = useState<IDataFilterHistoricoIntegracao>(() => {
        if (typedPersistedFilters && Object.keys(typedPersistedFilters).length > 0) {
            return typedPersistedFilters
        }
        return initialRetornoPEDFilterValue
    })
    const [numberPage, setNumberPage] = useState<number>(typedPersistedFilters?.numberPage || 0)
    const [prestadoresList, setprestadoresList] = useState<IGetHistoricoIntegracao[]>([])
    const [file, setFile] = useState([])
    const [openModal, setOpenModal] = useState(false)

    const updatePage = (newPage: number) => {
        setNumberPage(newPage)
        if (dataFilterHistorico && Object.keys(dataFilterHistorico).length > 0) {
            updatePersistedFilters({
                ...dataFilterHistorico,
                numberPage: newPage
            })
        }
    }

    function loadDados(competenciaInicial?: string) {
        setLoadingDados(true)

        HistoricoIntegracao.get({
            competencia: competenciaInicial ? competenciaInicial : dataFilterHistorico?.competencia,
            nomeCpfCnpjCodigo: dataFilterHistorico?.prestador,
            statusArquivo: dataFilterHistorico?.situacaoArquivo !== 'null' ? (dataFilterHistorico?.situacaoArquivo as StatusArquivoEnum) : null,
            tipoArquivo: dataFilterHistorico?.tipoArquivo !== 'null' ? (dataFilterHistorico?.tipoArquivo as TipoArquivoEnum) : null,
            statusRetornoArquivo: dataFilterHistorico?.tipoRetorno !== 'null' ? (dataFilterHistorico?.tipoRetorno as StatusRetornoArquivoEnum) : null,
            page: numberPage,
            size: 10
        })
            .then(({ data }) => {
                setprestadoresList(data?.content)
                setPagination(PaginationHelper.parserPagination(data, updatePage))
                setLoadingDados(false)
            })
            .catch((err) => {
                addToast({
                    title: err?.response?.data?.message ? err?.response?.data?.message : 'Ocorreu um erro: Desconhecido',
                    type: 'error',
                    duration: 5000
                })
            })
        // setprestadoresList(AcompanhamentoListMock)
    }

    const handleLimparFiltros = () => {
        const competenciaAtual = dataFilterHistorico?.competencia
        const filtrosLimpos = {
            ...initialRetornoPEDFilterValue,
            competencia: competenciaAtual
        }

        setDataFilterHistorico(filtrosLimpos)
        setNumberPage(0)
        setprestadoresList([])

        clearPersistedFilters()

        try {
            const key = '@filters:/acompanhamento:historico-integracao'
            sessionStorage.removeItem(key)
        } catch (error) {
            console.error('Erro ao limpar sessionStorage:', error)
        }

        HistoricoIntegracao.get({
            competencia: competenciaAtual || '',
            nomeCpfCnpjCodigo: '',
            statusArquivo: null,
            tipoArquivo: null,
            statusRetornoArquivo: null,
            page: 0,
            size: 10
        })
            .then(({ data }) => {
                setprestadoresList(data?.content)
                setPagination(PaginationHelper.parserPagination(data, updatePage))
            })
            .catch((err) => {
                addToast({
                    title: err?.response?.data?.message ? err?.response?.data?.message : 'Ocorreu um erro: Desconhecido',
                    type: 'error',
                    duration: 5000
                })
            })
    }

    function exportarPDF() {
        HistoricoIntegracao.getPDF({
            competencia: dataFilterHistorico?.competencia,
            nomeCpfCnpjCodigo: dataFilterHistorico?.prestador,
            statusArquivo: dataFilterHistorico?.situacaoArquivo !== 'null' ? (dataFilterHistorico?.situacaoArquivo as StatusArquivoEnum) : null,
            tipoArquivo: dataFilterHistorico?.tipoArquivo !== 'null' ? (dataFilterHistorico?.tipoArquivo as TipoArquivoEnum) : null,
            statusRetornoArquivo: dataFilterHistorico?.tipoRetorno !== 'null' ? (dataFilterHistorico?.tipoRetorno as StatusRetornoArquivoEnum) : null,
            page: numberPage,
            size: 10
        }).then((response) => {
            downloadFileV2(response.data, fileNameFromBlob(response?.headers['content-disposition']))
        })
    }

    function exportarExcel() {
        HistoricoIntegracao.getExcel({
            competencia: dataFilterHistorico?.competencia,
            nomeCpfCnpjCodigo: dataFilterHistorico?.prestador,
            statusArquivo: dataFilterHistorico?.situacaoArquivo !== 'null' ? (dataFilterHistorico?.situacaoArquivo as StatusArquivoEnum) : null,
            tipoArquivo: dataFilterHistorico?.tipoArquivo !== 'null' ? (dataFilterHistorico?.tipoArquivo as TipoArquivoEnum) : null,
            statusRetornoArquivo: dataFilterHistorico?.tipoRetorno !== 'null' ? (dataFilterHistorico?.tipoRetorno as StatusRetornoArquivoEnum) : null,
            page: numberPage,
            size: 10
        }).then((response) => {
            downloadFileV2(response.data, fileNameFromBlob(response?.headers['content-disposition']))
        })
    }

    useEffect(() => {
        if (dataFilterHistorico.competencia) {
            loadDados()
        }
    }, [numberPage])

    useEffect(() => {
        if (typedPersistedFilters && typedPersistedFilters.competencia && !prestadoresList.length) {
            loadDados()
        }
    }, [typedPersistedFilters])

    useEffect(() => {
        if (dataFilterHistorico && Object.keys(dataFilterHistorico).length > 0) {
            updatePersistedFilters({
                ...dataFilterHistorico,
                numberPage
            })
        }
    }, [dataFilterHistorico, numberPage])

    return (
        <>
            <S.Wrapper>
                <FitroHistoricoIntegracao
                    dataFilter={dataFilterHistorico}
                    setDataFilter={setDataFilterHistorico}
                    buscar={(e) => {
                        setNumberPage(0)
                        loadDados(e)
                    }}
                />

                {dataFilterHistorico &&
                    Object.keys(dataFilterHistorico).length > 0 &&
                    (dataFilterHistorico.competencia ||
                        dataFilterHistorico.prestador ||
                        dataFilterHistorico.situacaoArquivo !== 'null' ||
                        dataFilterHistorico.tipoArquivo !== 'null' ||
                        dataFilterHistorico.tipoRetorno !== 'null') && (
                        <div style={{ marginBottom: '16px', marginRight: '20px', textAlign: 'right' }}>
                            <Button variant="outlined" color="secondary" onClick={handleLimparFiltros}>
                                Limpar Filtros
                            </Button>
                        </div>
                    )}
            </S.Wrapper>

            {loadingDados ? (
                <Box sx={{ display: 'flex', width: '100%', justifyContent: 'center', alignItems: 'center', height: '70vh' }}>
                    <CircularProgress />
                </Box>
            ) : (
                <>
                    {/* <Alert severity="warning" title="Arquivo PED em processamento">
                <AlertTitle>Foram encontrados erros durante a importação</AlertTitle>O tempo do processamento varia de acordo com a quantidade de
                dados
            </Alert> */}
                    {prestadoresList?.length > 0 ? (
                        <>
                            <div>
                                <SectionContainer title="Prestadores" style={{ gap: '0px' }}>
                                    <div className="row-result">
                                        {<TableResults pageSize={pagination?.linhasPorPagina} total={pagination?.totalRegistros} page={numberPage} />}
                                    </div>
                                    <div style={{ width: '100%' }}>
                                        <S.ContentItem>
                                            <S.Item>
                                                <p>CNPJ</p>
                                            </S.Item>
                                            <S.Item>
                                                <p>Prestador</p>
                                            </S.Item>
                                            <S.Item>
                                                <p>Competência</p>
                                            </S.Item>
                                            <S.Item>
                                                <p>Situação</p>
                                            </S.Item>
                                            <S.Item>
                                                <p></p>
                                            </S.Item>
                                        </S.ContentItem>
                                        {prestadoresList?.map((item, index) => {
                                            return <ItemList item={item} key={index} />
                                        })}
                                    </div>
                                    {
                                        <PaginationNew
                                            totalPage={pagination?.totalPaginas}
                                            totalRegister={pagination?.totalRegistros}
                                            actualPage={pagination?.paginaAtual}
                                            setNumberPage={pagination?.setNumberPage}
                                        />
                                    }
                                </SectionContainer>

                                <S.ButtonAction>
                                    <Button color="warning" onClick={() => setOpenModal(true)}>
                                        Exportar Relatório
                                    </Button>
                                </S.ButtonAction>
                            </div>
                            <ExportarHistoricoModal
                                openModal={openModal}
                                setOpenModal={setOpenModal}
                                exportarPDF={exportarPDF}
                                exportarExcel={exportarExcel}
                            />
                        </>
                    ) : (
                        <NoItensComponent
                            src="/contas/assets/imgs/notfound.svg"
                            titulo="Ops!... nenhum resultado encontrado"
                            subTitulo="Tente outra pesquisa"
                        />
                    )}
                </>
            )}
        </>
    )
}

export default HistoricoIntegracaoComponent
