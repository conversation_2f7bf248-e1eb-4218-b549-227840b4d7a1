/* eslint-disable react-hooks/exhaustive-deps */
import * as S from './styles'
import { useState, useEffect } from 'react'
import { useFilterPersistence } from 'hooks/storage/useFilterPersistence'
import GerarDemonstrativoComponent from './GerarDemonstrativo'
import RetornoDemonstrativoComponent from './RetornoDemonstrativo'

const DemonstrativoTemplate = () => {
    const [currentStep, setCurrentStep] = useState(0)

    // Hook para persistência da aba ativa
    const { filters: persistedTab, updateFilters: updatePersistedTab } = useFilterPersistence({
        route: '/demonstrativo',
        filterType: 'active-tab',
        defaultValues: {
            currentStep: 0
        }
    })

    useEffect(() => {
        if (persistedTab && persistedTab.currentStep !== undefined) {
            setCurrentStep(persistedTab.currentStep)
        }
    }, [])

    useEffect(() => {
        updatePersistedTab({ currentStep })
    }, [currentStep])

    return (
        <S.BodyContainer>
            <S.CardListOptions>
                <S.Button onClick={() => setCurrentStep(0)} actived={currentStep === 0}>
                    <p>Gerar demonstrativo</p>
                </S.Button>
                <S.Button onClick={() => setCurrentStep(1)} actived={currentStep === 1}>
                    <p>Consultar Demostrativo</p>
                </S.Button>
            </S.CardListOptions>
            {currentStep === 0 && <GerarDemonstrativoComponent />}

            {currentStep === 1 && <RetornoDemonstrativoComponent />}
        </S.BodyContainer>
    )
}

export default DemonstrativoTemplate

