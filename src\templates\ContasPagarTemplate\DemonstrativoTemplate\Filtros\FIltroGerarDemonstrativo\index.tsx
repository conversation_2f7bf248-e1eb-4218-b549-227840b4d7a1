/* eslint-disable react-hooks/exhaustive-deps */
import { FormControl, InputLabel, MenuItem, Select, TextField } from '@mui/material'
import { Theme, useTheme } from '@mui/material/styles'
import { useFilterPersistence } from 'hooks/storage/useFilterPersistence'
import { Dispatch, SetStateAction, useEffect, useState } from 'react'
import { ReactSVG } from 'react-svg'
import { Competencia } from 'src/services/ContasPagar/competencia-controller'
import { parseDataToMMYYYY } from 'utils/functions'
import { IDataFilterGerarDemonstrativo } from '../filtro-types'

import AsyncSimpleSelect from 'components/atoms/AsyncSimpleSelect'
import BadgeSelect from 'components/atoms/BadgeSelect'
import { apiContasPagar } from 'src/services/api/contas-a-pagar'
import { selectTipoPrestadorOptions } from './mocksSelec'
import * as S from './styles'

type FitroGerarDemonstrativo = {
    dataFilter: IDataFilterGerarDemonstrativo
    setDataFilter: Dispatch<SetStateAction<IDataFilterGerarDemonstrativo>>
    buscar: (data?: string) => void
}

type typeOptions = {
    label: string
    value: string
}

type SelectedController = {
    municipioSelect: boolean
    segregadoSelect: boolean
}

const FitroGerarDemonstrativo = ({ dataFilter, setDataFilter, buscar }: FitroGerarDemonstrativo) => {
    // Hook para persistência dos filtros visuais
    const { filters: persistedVisualFilters, updateFilters: updatePersistedVisualFilters } = useFilterPersistence({
        route: '/demonstrativo',
        filterType: 'gerar-demonstrativo-visual',
        defaultValues: {
            municipios: [],
            municipiosSegregados: []
        }
    })

    const [competenciaOptions, setCompetenciaOptions] = useState<typeOptions[]>([])
    const [loadingFilter, setLoadingFilter] = useState(true)
    const [municipios, setMunicipios] = useState<typeOptions[]>(persistedVisualFilters?.municipios || [])
    const [municipiosSegregados, setMunicipiosSegregados] = useState<typeOptions[]>(persistedVisualFilters?.municipiosSegregados || [])
    const [selectDisabledController, setSelectDisabledController] = useState<SelectedController>({ municipioSelect: false, segregadoSelect: false })

    function getCompetencias() {
        Competencia.get().then(({ data }) => {
            const parseCompetenciaOptions: typeOptions[] = data?.map((item) => ({
                label: parseDataToMMYYYY(item?.competencia?.toString(), 'MM/YYYY'),
                value: item?.competencia?.toString()
            }))
            setCompetenciaOptions(parseCompetenciaOptions)

            if (!dataFilter?.competencia) {
                const competenciaPadrao = data?.[data?.length > 0 ? data?.length - 1 : 0]?.competencia?.toString()
                setDataFilter({ ...dataFilter, competencia: competenciaPadrao })
            }

            setLoadingFilter(false)
        })
    }

    function DeletarMunicipio(municipio: typeOptions, segregado: boolean) {
        if (segregado) {
            const municipiosAtualizados = municipiosSegregados?.filter((item) => {
                return item.value !== municipio.value
            })

            setMunicipiosSegregados(municipiosAtualizados)
            setDataFilter({ ...dataFilter, municipios: parserMunicipioToIBGE(municipiosAtualizados) })
        } else {
            const municipiosAtualizados = municipios?.filter((item) => {
                return item.value !== municipio.value
            })

            setMunicipios(municipiosAtualizados)
            setDataFilter({ ...dataFilter, municipios: parserMunicipioToIBGE(municipiosAtualizados) })
        }
    }

    const parserMunicipioToIBGE = (props: typeOptions[]) => {
        return props.map((item) => item?.value)
    }

    const parserMunicipio = (props): typeOptions[] => {
        return props.map((item) => ({
            label: item?.municipio,
            value: item?.codigoIbge
        }))
    }

    async function loadOptionsMunicipios(inputContent: string) {
        return (
            apiContasPagar
                // .get(`/prestador?cpfCnpj=${inputContent}`)
                .get(`/municipio/buscar?nomeMunicipio=${inputContent}`)
                .then(({ data }) => {
                    return parserMunicipio(data)
                })
        )
    }

    useEffect(() => {
        if (municipios?.length === 0 && municipiosSegregados?.length === 0) {
            setSelectDisabledController({ municipioSelect: false, segregadoSelect: false })
            setDataFilter({ ...dataFilter, municipios: null, segregarMunicipios: null })
        } else {
            if (municipios?.length > 0) {
                setSelectDisabledController({ municipioSelect: false, segregadoSelect: true })
                setDataFilter({ ...dataFilter, municipios: parserMunicipioToIBGE(municipios), segregarMunicipios: false })
            }
            if (municipiosSegregados?.length > 0) {
                setSelectDisabledController({ municipioSelect: true, segregadoSelect: false })
                setDataFilter({ ...dataFilter, municipios: parserMunicipioToIBGE(municipiosSegregados), segregarMunicipios: true })
            }
        }
    }, [municipios, municipiosSegregados])

    useEffect(() => {
        getCompetencias()
    }, [])

    useEffect(() => {
        updatePersistedVisualFilters({
            municipios,
            municipiosSegregados
        })
    }, [municipios, municipiosSegregados])

    return (
        <>
            {loadingFilter ? (
                <></>
            ) : (
                <S.BodyContainer>
                    <S.FilterRow0>
                        <FormControl>
                            <InputLabel required>Competência</InputLabel>
                            <Select
                                value={dataFilter?.competencia || ''}
                                onChange={(e) => {
                                    setDataFilter({
                                        ...dataFilter,
                                        competencia: e?.target?.value
                                    })
                                }}
                            >
                                {competenciaOptions?.map((item, index) => {
                                    return (
                                        <MenuItem key={index} value={item?.value}>
                                            {item?.label}
                                        </MenuItem>
                                    )
                                })}
                            </Select>
                        </FormControl>

                        <FormControl>
                            <InputLabel required>Tipo de prestador</InputLabel>
                            <Select
                                value={dataFilter?.tipo || ''}
                                onChange={(e) => {
                                    setDataFilter({
                                        ...dataFilter,
                                        tipo: e?.target?.value as 'CREDENCIADO' | 'REFERENCIADO_EVENTUAL'
                                    })
                                }}
                            >
                                {selectTipoPrestadorOptions?.map((item, index) => {
                                    return (
                                        <MenuItem key={index} value={item?.value}>
                                            {item?.label}
                                        </MenuItem>
                                    )
                                })}
                            </Select>
                        </FormControl>

                        <S.SearchWrapper>
                            {/* <PrestadorInput handleOnChange={(e) => setDataFilter({ ...dataFilter, prestadorUUID: e?.uuid })} /> */}
                            <TextField
                                label="Prestador"
                                fullWidth
                                value={dataFilter?.prestador || ''}
                                onChange={({ target: { value } }) => setDataFilter({ ...dataFilter, prestador: value })}
                            />
                            <S.IconSearchWrapper
                                onClick={() => {
                                    buscar()
                                }}
                            >
                                <ReactSVG src="/contas/assets/imgs/mai-ic-search.svg" />
                            </S.IconSearchWrapper>
                        </S.SearchWrapper>
                    </S.FilterRow0>
                    <S.FilterRow1>
                        <S.SelectWrapper>
                            <AsyncSimpleSelect
                                label="Municípios"
                                value={municipios}
                                disabled={selectDisabledController?.municipioSelect}
                                // value={municipios?.[municipios?.length - 1]}
                                defaultValue={municipios}
                                onChange={(e: typeOptions) => {
                                    if (municipios?.length === 0) {
                                        setMunicipios([e])
                                    } else {
                                        if (!municipios?.includes(e)) {
                                            setMunicipios([...municipios, e])
                                        }
                                    }
                                }}
                                // isClearable
                                loadOptions={loadOptionsMunicipios}
                            />
                            {municipios?.length > 0 && (
                                <S.SelectedMunicipality>
                                    {municipios?.map((item, index) => {
                                        return (
                                            <BadgeSelect key={index} deletarMunicipio={DeletarMunicipio} municipio={item}>
                                                {item?.label}
                                            </BadgeSelect>
                                        )
                                    })}
                                </S.SelectedMunicipality>
                            )}
                        </S.SelectWrapper>

                        <S.SelectWrapper>
                            <AsyncSimpleSelect
                                label="Municípios Segregados"
                                disabled={selectDisabledController?.segregadoSelect}
                                value={municipiosSegregados}
                                // value={municipios?.[municipios?.length - 1]}
                                defaultValue={municipiosSegregados}
                                onChange={(e: typeOptions) => {
                                    if (municipiosSegregados?.length === 0) {
                                        setMunicipiosSegregados([e])
                                    } else {
                                        if (!municipiosSegregados?.includes(e)) {
                                            setMunicipiosSegregados([...municipiosSegregados, e])
                                        }
                                    }
                                }}
                                // isClearable
                                loadOptions={loadOptionsMunicipios}
                            />
                            {municipiosSegregados?.length > 0 && (
                                <S.SelectedMunicipality>
                                    {municipiosSegregados?.map((item, index) => {
                                        return (
                                            <BadgeSelect key={index} deletarMunicipio={DeletarMunicipio} municipio={item} segregado={true}>
                                                {item?.label}
                                            </BadgeSelect>
                                        )
                                    })}
                                </S.SelectedMunicipality>
                            )}
                        </S.SelectWrapper>

                        {/* <FormControl fullWidth>
                            <InputLabel>Segregar Municípios</InputLabel>
                            <Select
                                multiple
                                value={dataFilter?.segregarMunicipios ? dataFilter?.segregarMunicipios : []}
                                onChange={(e) => {
                                    setDataFilter({ ...dataFilter, segregarMunicipios: e?.target?.value as string[] })
                                }}
                                renderValue={(selected) => (
                                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                                        {selected.map((value) => (
                                            <Chip key={value} label={value} />
                                        ))}
                                    </Box>
                                )}
                                // MenuProps={MenuProps}
                            >
                                {SegregarMunicipiosMock?.map((name) => (
                                    <MenuItem key={name} value={name} style={getStyles(name, dataFilter?.segregarMunicipios, theme)}>
                                        {name}
                                    </MenuItem>
                                ))}
                            </Select>
                        </FormControl> */}
                    </S.FilterRow1>
                    <S.InfoWrapper>
                        <ReactSVG src="/contas/assets/icons/mai-ic-info.mono.svg" />
                        <span>
                            Os filtros retornam apenas prestadores com arquivo <strong>Retorno EMP</strong> importado com sucesso.
                        </span>
                    </S.InfoWrapper>
                </S.BodyContainer>
            )}
        </>
    )
}

export default FitroGerarDemonstrativo
