import { useState, useEffect } from 'react'

interface FilterPersistenceOptions {
    route: string
    filterType: string
    defaultValues?: any
}

/**
 * Hook para persistência genérica de filtros no sessionStorage
 * @param options - Opções de configuração
 * @param options.route - Rota atual (ex: '/example')
 * @param options.filterType - Tipo de filtro (ex: 'example')
 * @param options.defaultValues - Valores padrão para os filtros
 * @returns Objeto com filtros e funções de persistência
 */

export const useFilterPersistence = (options: FilterPersistenceOptions) => {
    const { route, filterType, defaultValues = {} } = options

    const getFilterKey = () => `@filters:${route}:${filterType}`

    const saveFilters = (values: any) => {
        try {
            const key = getFilterKey()
            sessionStorage.setItem(key, JSON.stringify(values))
        } catch (error) {
            console.error('Erro ao salvar filtros:', error)
        }
    }

    const loadFilters = () => {
        try {
            const key = getFilterKey()
            const stored = sessionStorage.getItem(key)
            return stored ? JSON.parse(stored) : defaultValues
        } catch (error) {
            console.error('Erro ao carregar filtros:', error)
            return defaultValues
        }
    }

    const clearFilters = () => {
        try {
            const key = getFilterKey()
            sessionStorage.removeItem(key)
        } catch (error) {
            console.error('Erro ao limpar filtros:', error)
        }
    }

    const [filters, setFilters] = useState(loadFilters)

    useEffect(() => {
        saveFilters(filters)
    }, [filters])

    const updateFilters = (newFilters: any) => {
        setFilters(newFilters)
    }

    return {
        filters,
        saveFilters,
        loadFilters,
        clearFilters,
        updateFilters
    }
}
