/* eslint-disable react-hooks/exhaustive-deps */
import { FormControl, InputLabel, MenuItem, Select, TextField } from '@mui/material'
import { useFilterPersistence } from 'hooks/storage/useFilterPersistence'
import { Dispatch, SetStateAction, useEffect, useState } from 'react'
import { ReactSVG } from 'react-svg'
import { Competencia } from 'src/services/ContasPagar/competencia-controller'
import { parseDataToMMYYYY } from 'utils/functions'
import { IDataFilterGerarDemonstrativo } from '../filtro-types'
import * as S from './styles'

type FitroRetornoDemonstrativo = {
    dataFilter: IDataFilterGerarDemonstrativo
    setDataFilter: Dispatch<SetStateAction<IDataFilterGerarDemonstrativo>>
    buscar: (data?: string) => void
}

type typeOptions = {
    label: string
    value: string
}

const FitroRetornoDemonstrativo = ({ dataFilter, setDataFilter, buscar }: FitroRetornoDemonstrativo) => {
    // Hook para persistência dos filtros visuais
    const { filters: persistedVisualFilters, updateFilters: updatePersistedVisualFilters } = useFilterPersistence({
        route: '/demonstrativo',
        filterType: 'consultar-demonstrativo-visual',
        defaultValues: {
            competencia: '',
            prestador: ''
        }
    })

    const [competenciaOptions, setCompetenciaOptions] = useState<typeOptions[]>([])
    const [loadingFilter, setLoadingFilter] = useState(true)

    function getCompetencias() {
        Competencia.get().then(({ data }) => {
            const competenciaOptions: typeOptions[] = data?.map((item) => ({
                label: parseDataToMMYYYY(item?.competencia?.toString(), 'MM/YYYY'),
                value: item?.competencia?.toString()
            }))

            setCompetenciaOptions(competenciaOptions)

            if (!dataFilter?.competencia) {
                const competenciaPadrao = data?.[data?.length > 0 ? data?.length - 1 : 0]?.competencia?.toString()
                setDataFilter({ ...dataFilter, competencia: competenciaPadrao })
            }

            setLoadingFilter(false)
        })
    }

    useEffect(() => {
        getCompetencias()
    }, [])

    useEffect(() => {
        if (dataFilter) {
            updatePersistedVisualFilters({
                competencia: dataFilter.competencia,
                prestador: dataFilter.prestador
            })
        }
    }, [dataFilter])
    return (
        <>
            {loadingFilter ? (
                <></>
            ) : (
                <S.BodyContainer>
                    <S.FilterRow0>
                        <FormControl>
                            <InputLabel required>Competência</InputLabel>
                            <Select
                                defaultValue={dataFilter?.competencia}
                                // value={dataFilter?.competencia}

                                onChange={(e) => {
                                    setDataFilter({
                                        ...dataFilter,
                                        competencia: e?.target?.value
                                    })
                                }}
                            >
                                {competenciaOptions?.map((item, index) => {
                                    return (
                                        <MenuItem key={index} value={item?.value}>
                                            {item?.label}
                                        </MenuItem>
                                    )
                                })}
                            </Select>
                        </FormControl>

                        <S.SearchWrapper>
                            {/* <PrestadorInput handleOnChange={(e) => setDataFilter({ ...dataFilter, prestadorUUID: e?.uuid })} /> */}
                            <TextField
                                label="Prestador"
                                fullWidth
                                value={dataFilter?.prestador}
                                defaultValue={dataFilter?.prestador}
                                onChange={({ target: { value } }) => setDataFilter({ ...dataFilter, prestador: value })}
                            />
                            <S.IconSearchWrapper
                                onClick={() => {
                                    buscar()
                                }}
                            >
                                <ReactSVG src="/contas/assets/imgs/mai-ic-search.svg" />
                            </S.IconSearchWrapper>
                        </S.SearchWrapper>
                    </S.FilterRow0>
                </S.BodyContainer>
            )}
        </>
    )
}

export default FitroRetornoDemonstrativo

