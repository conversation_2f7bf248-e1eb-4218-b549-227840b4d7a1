/* eslint-disable react-hooks/exhaustive-deps */
import {
    Box,
    Button,
    Checkbox,
    CircularProgress,
    FormControl,
    FormControlLabel,
    InputLabel,
    MenuItem,
    Radio,
    RadioGroup,
    Select,
    TextField
} from '@mui/material'
import AsyncSimpleSelect from 'components/atoms/AsyncSimpleSelect'
import BadgeSelect from 'components/atoms/BadgeSelect'
import NoItensComponent from 'components/atoms/NoItensComponent'
import Modal from 'components/molecules/Modal'
import Pagination from 'components/molecules/Pagination'
import { useFilterPersistence } from 'hooks/storage/useFilterPersistence'
import { useToast } from 'hooks/toast'
import { useRouter } from 'next/router'
import React, { useEffect } from 'react'
import { ReactSVG } from 'react-svg'
import { RecolhimentoIss } from 'services/ContasPagar/rascunho-recolhimento-iss-controller'
import { SituacaoRecolhimentoEnum } from 'services/ContasPagar/rascunho-recolhimento-iss-controller/enum'
import { IRascunhoRecolhimentoIssDto, IRecolhimentoIssListagemDto } from 'services/ContasPagar/rascunho-recolhimento-iss-controller/types'
import { Competencia } from 'src/services/ContasPagar/competencia-controller'
import { Municipios } from 'src/services/ContasPagar/municipios-controller'
import { Prestador } from 'src/services/ContasPagar/prestador-controller'
import { RecolhimentoISSService } from 'src/services/ContasPagar/recolhimento-iss'
import {
    IRecolhimenoIssRelatorioPrestadorDTO,
    IRecolhimentoIssListagemDTO,
    IRecolhimentoIssRelatorioDTO,
    IRecolhimentoIssRelatorioTotaisDTO
} from 'src/services/ContasPagar/recolhimento-iss/types'
import { IPagination, ISortPage } from 'src/types/pagination'
import { downloadFileV2, parseDataToMMYYYY } from 'utils/functions'
import { PaginationHelper } from 'utils/pagination-helper'
import * as S from './styles'

type typeOptions = {
    label: string
    value: string
}

type filterValueTypes = {
    competencia: string
    municipio?: string[]
    situacao?: SituacaoRecolhimentoEnum[]
    prestador?: number[]
    dataPagamentoIssInicial?: string
    dataPagamentoIssFinal?: string
}

const SelecionarPrestadoresRecolhimentoISSTemplate = () => {
    const route = useRouter()
    const { addToast } = useToast()
    const [fieldValues, setFieldValues] = React.useState<filterValueTypes & ISortPage>()
    const [filterValues, setFilterValues] = React.useState<IRascunhoRecolhimentoIssDto>()
    const [loadingDados, setLoadingDados] = React.useState(true)
    const [currentStep, setCurrentStep] = React.useState(0)
    const [pagination, setPagination] = React.useState<IPagination>()
    const [numberPage, setNumberPage] = React.useState<number>(0)
    const [prestadoresList, setPrestadoresList] = React.useState<IRecolhimentoIssListagemDto[]>([])
    const [totalElements, setTotalElements] = React.useState<number>(0)
    const [prestadoresSelecionados, setPrestadoresSelecionados] = React.useState<number[]>([])
    const [prestadoresRemovidos, setPrestadoresRemovidos] = React.useState<number[]>([])
    const [selecionarTodos, setSelecionarTodos] = React.useState(false)
    const [competenciaOptions, setCompetenciaOptions] = React.useState<typeOptions[]>()
    const [municipiosOptions, setMunicipiosOptions] = React.useState<typeOptions[]>()
    const [municipiosSelecionados, setMunicipiosSelecionados] = React.useState<typeOptions[]>([])
    const [situacaoSelecionados, setSituacoesSelecionados] = React.useState<typeOptions[]>([])
    const [prestadoresOptionsSelecionados, setPrestadoresOptionsSelecionados] = React.useState<typeOptions[]>([])
    const [openFilter, setOpenFilter] = React.useState<boolean>(false)
    const [relatoriosList, setRelatoriosList] = React.useState<IRecolhimentoIssRelatorioDTO[]>([])
    const [modal, setModal] = React.useState<boolean>(false)
    const [fileType, setFileType] = React.useState<'pdf' | 'excel'>('pdf')
    const [situacaoOptions, setSituacaoOptions] = React.useState<typeOptions[]>()

    // Hook para persistência dos filtros
    const {
        filters: persistedFilters,
        updateFilters: updatePersistedFilters,
        clearFilters: clearPersistedFilters
    } = useFilterPersistence({
        route: '/recolhimento-iss',
        filterType: 'recolhimento-iss',
        defaultValues: {
            competencia: '',
            municipio: [],
            situacao: [],
            prestador: [],
            dataPagamentoIssInicial: '',
            dataPagamentoIssFinal: '',
            numberPage: 0
        }
    })

    const { filters: persistedTab, updateFilters: updatePersistedTab } = useFilterPersistence({
        route: '/recolhimento-iss',
        filterType: 'active-tab',
        defaultValues: {
            currentStep: 0
        }
    })

    const {
        filters: persistedVisualFilters,
        updateFilters: updatePersistedVisualFilters,
        clearFilters: clearPersistedVisualFilters
    } = useFilterPersistence({
        route: '/recolhimento-iss',
        filterType: 'visual-filters',
        defaultValues: {
            municipiosSelecionados: [],
            situacoesSelecionados: [],
            prestadoresOptionsSelecionados: []
        }
    })

    const titles: { label: string; value: keyof IRecolhimentoIssListagemDTO | 'acao' }[] = [
        { label: 'Nome do prestador', value: 'nome' },
        { label: 'CNPJ', value: 'cnpj' },
        { label: 'Competência', value: 'competencia' },
        { label: 'Pagamento do ISS', value: 'dataPagamentoIss' },
        { label: 'Situação', value: 'situacaoDescricao' }
    ]

    const titlesRelatorios: {
        label: string
        value: keyof IRecolhimentoIssRelatorioDTO | keyof IRecolhimenoIssRelatorioPrestadorDTO
    }[] = [
        { label: 'Prestador', value: 'nome' },
        { label: 'Situação', value: 'situacao' },
        { label: 'Nº Empenho', value: 'numeroEmp' },
        { label: 'Nº da NOB', value: 'numeroNobIss' },
        { label: 'Nº da NF', value: 'numeroNota' },
        { label: 'ISS retido', value: 'valorRetencaoIss' },
        { label: 'Total retido', value: 'valorTotalRetidoIss' },
        { label: 'Pago', value: 'valorTotalPagoIss' },
        { label: 'Saldo a pagar', value: 'saldoAPagarIss' }
    ]

    const titlesSomatorioRelatorios: { label: string; value: keyof IRecolhimentoIssRelatorioTotaisDTO }[] = [
        { label: 'ISS retido', value: 'somaValorRetencaoIss' },
        { label: 'Total retido', value: 'somaValorRetencaoIss' },
        { label: 'Pago', value: 'somaValorTotalPagoIss' },
        { label: 'Saldo a pagar', value: 'somaSaldoAPagarIss' }
    ]

    async function loadDadosRelatorios(filters: {
        competencia: Date | string
        municipio?: string[]
        situacao?: SituacaoRecolhimentoEnum[]
        prestador?: number[]
        dataPagamentoIssInicial?: string
        datePagamentoIssFinal?: string
    }) {
        return RecolhimentoISSService.getRelatorio(filters)
            .then(({ data }) => {
                setLoadingDados(false)
                setRelatoriosList(data)
                return data
            })
            .catch((err) => {
                addToast({
                    title: err?.response?.data?.message ? err?.response?.data?.message : 'Ocorreu um erro: Desconhecido',
                    type: 'error',
                    duration: 5000
                })
            })
    }

    async function loadOptionSituacao() {
        return RecolhimentoISSService.getSituacoes().then(({ data }) => {
            setSituacaoOptions(data.map((element) => ({ label: element?.descricao, value: element?.valor })))
            return data
        })
    }

    async function loadOptionsPrestador(nomeCNPJ: string) {
        return Prestador.getPorNomeCnpj({ nomeCNPJ, page: 0, size: 100 }).then(({ data }) => {
            return data?.content?.map((element) => ({ label: element?.nome, value: element?.id + '' }))
        })
    }

    async function loadOptionsCompetencias() {
        return Competencia.getIss().then(({ data }) => {
            setCompetenciaOptions(
                data?.map((item) => ({
                    label: parseDataToMMYYYY(item?.competencia?.toString(), 'MM/YYYY'),
                    value: item?.competencia?.toString()
                }))
            )
            return data
        })
    }

    async function loadOptionsMunicipios(text: string) {
        return Municipios.get(text).then(({ data }) => {
            setMunicipiosOptions(
                data?.map((item) => ({
                    label: item?.municipio,
                    value: item?.codigoIbge
                }))
            )
            return data
        })
    }

    async function downloadRelatorio(
        filters: {
            competencia: Date | string
            municipio?: string[]
            situacao?: SituacaoRecolhimentoEnum[]
            prestador?: number[]
            dataPagamentoIssInicial?: string
            datePagamentoIssFinal?: string
        },
        type: 'pdf' | 'excel'
    ) {
        if (type === 'excel')
            return RecolhimentoISSService.getDownloadExcel(filters).then(({ data }) => {
                downloadFileV2(data, 'Relatorio de Controle.xlsx')
                return data
            })

        if (type === 'pdf')
            return RecolhimentoISSService.getDownloadPdf(filters).then(({ data }) => {
                downloadFileV2(data, 'Relatorio de controle.pdf')
                return data
            })
    }

    function LimparFiltro() {
        setFieldValues(undefined)
        setFilterValues(undefined)
        setPrestadoresOptionsSelecionados([])
        setMunicipiosSelecionados([])
        setSituacoesSelecionados([])

        clearPersistedFilters()
        clearPersistedVisualFilters()

        setCurrentStep(0)

        if (competenciaOptions && competenciaOptions.length > 0) {
            const competenciaPadrao = competenciaOptions.reverse()[0]?.value
            setFieldValues({ competencia: competenciaPadrao })
        }
    }

    React.useEffect(() => {
        loadOptionsCompetencias().then((data) => {
            const competencia = data?.reverse()?.[0]?.competencia?.toString()

            if (persistedFilters && Object.keys(persistedFilters).length > 0) {
                const filtersToApply = {
                    ...persistedFilters,
                    competencia: persistedFilters.competencia || competencia
                }
                setFieldValues(filtersToApply)

                if (persistedVisualFilters) {
                    if (persistedVisualFilters.municipiosSelecionados) {
                        setMunicipiosSelecionados(persistedVisualFilters.municipiosSelecionados)
                    }
                    if (persistedVisualFilters.situacoesSelecionados) {
                        setSituacoesSelecionados(persistedVisualFilters.situacoesSelecionados)
                    }
                    if (persistedVisualFilters.prestadoresOptionsSelecionados) {
                        setPrestadoresOptionsSelecionados(persistedVisualFilters.prestadoresOptionsSelecionados)
                    }
                }
            } else {
                setFieldValues({ ...fieldValues, competencia })
            }
        })
        loadOptionsMunicipios('')
        loadOptionSituacao()
    }, [])

    React.useEffect(() => {
        if (persistedTab && persistedTab.currentStep !== undefined) {
            setCurrentStep(persistedTab.currentStep)
        }
    }, [])

    React.useEffect(() => {
        if (persistedFilters && persistedFilters.numberPage !== undefined) {
            setNumberPage(persistedFilters.numberPage)
        }
    }, [persistedFilters])

    const updatePage = (newPage: number) => {
        setNumberPage(newPage)
        if (fieldValues && Object.keys(fieldValues).length > 0) {
            updatePersistedFilters({
                ...fieldValues,
                numberPage: newPage
            })
        }
    }

    const handleClickToSearchBtn = (value: filterValueTypes, prestadoresSelecionados: number[], prestadoresRemovidos: number[]) => {
        RecolhimentoIss?.putBuscar({
            definicao: {
                filtroCompetencia: value?.competencia?.toString(),
                filtroDataFimPagamentoIss: value?.dataPagamentoIssFinal?.toString(),
                filtroDataInicioPagamentoIss: value?.dataPagamentoIssInicial?.toString(),
                filtroMunicipios: value?.municipio,
                // filtroPrestadoresSelecionados: value?.prestador,
                filtroPrestadoresSelecionados: selecionarTodos ? [] : value?.prestador,
                filtroSituacaoRecolhimento: value?.situacao,
                pcsExcluidos: selecionarTodos ? prestadoresRemovidos : [],
                pcsSelecionados: !selecionarTodos ? prestadoresSelecionados : [],
                rascunhoId: null,
                selecionarTodos: selecionarTodos
            },
            rascunhoId: null
        }).then(({ data }) => {
            // setLoadingDados(true)
            route.push(`/recolhimento-iss/controle-recolhimento-verificacao?rascunhoId=${data?.rascunhoId}`)
        })
    }

    const listarPrestadores = (numberPage: number, value: filterValueTypes, prestadoresSelecionados: number[], prestadoresRemovidos: number[]) => {
        RecolhimentoIss?.postListarPrestadores(
            { page: numberPage, size: 10 },
            {
                filtroCompetencia: value?.competencia,
                filtroDataFimPagamentoIss: value?.dataPagamentoIssFinal?.toString() || null,
                filtroDataInicioPagamentoIss: value?.dataPagamentoIssInicial?.toString() || null,
                filtroMunicipios: value?.municipio || null,
                filtroPrestadoresSelecionados: value?.prestador || null,
                filtroSituacaoRecolhimento: value?.situacao || null,
                pcsExcluidos: selecionarTodos ? prestadoresSelecionados : [],
                pcsSelecionados: !selecionarTodos ? prestadoresRemovidos : [],
                rascunhoId: null,
                selecionarTodos: selecionarTodos
            }
        )
            .then(({ data }) => {
                setLoadingDados(false)
                setPrestadoresList(data?.content)

                if (
                    fieldValues?.competencia &&
                    (value?.prestador?.length === 0 || value?.prestador === undefined)
                    // &&
                    // (value?.municipio?.length === 0 || value?.municipio === undefined) &&
                    // (value?.situacao?.length === 0 || value?.situacao === undefined) &&
                    // (!value?.dataPagamentoIssInicial || !value?.dataPagamentoIssFinal)
                ) {
                    setTotalElements(data?.totalElements)
                }

                setPagination(PaginationHelper.parserPagination(data, updatePage))
            })
            .catch((err) => {
                addToast({
                    title: err?.response?.data?.message ? err?.response?.data?.message : 'Ocorreu um erro: Desconhecido',
                    type: 'error',
                    duration: 5000
                })
            })
    }

    useEffect(() => {
        if (fieldValues?.competencia) listarPrestadores(numberPage, fieldValues, prestadoresSelecionados, prestadoresRemovidos)
    }, [fieldValues?.competencia, numberPage, selecionarTodos])

    useEffect(() => {
        if (fieldValues && Object.keys(fieldValues).length > 0) {
            updatePersistedFilters(fieldValues)
        }
    }, [fieldValues])

    useEffect(() => {
        updatePersistedVisualFilters({
            municipiosSelecionados,
            situacoesSelecionados: situacaoSelecionados,
            prestadoresOptionsSelecionados
        })
    }, [municipiosSelecionados, situacaoSelecionados, prestadoresOptionsSelecionados])

    // Persistir aba ativa sempre que currentStep mudar
    useEffect(() => {
        updatePersistedTab({ currentStep })
    }, [currentStep])

    return (
        <S.BodyContainer>
            <S.Title>Recolhimento de ISS</S.Title>
            <S.CardListOptions>
                <S.Button onClick={() => setCurrentStep(0)} actived={currentStep === 0}>
                    <p>Controle</p>
                </S.Button>
                <S.Button onClick={() => setCurrentStep(1)} actived={currentStep === 1}>
                    <p>Relatório de Controle</p>
                </S.Button>
            </S.CardListOptions>
            <S.FilterBodyContainer>
                <S.FilterRow0>
                    <FormControl>
                        <InputLabel>Competência (mês da retenção do ISS)</InputLabel>
                        <Select
                            defaultValue={fieldValues?.competencia}
                            key={`${fieldValues?.competencia}`}
                            onChange={(e) => {
                                setFieldValues({
                                    ...fieldValues,
                                    competencia: e?.target?.value
                                })
                            }}
                        >
                            {competenciaOptions?.map((item, index) => {
                                return (
                                    <MenuItem key={index} value={item?.value}>
                                        {item?.label}
                                    </MenuItem>
                                )
                            })}
                        </Select>
                    </FormControl>
                    <FormControl>
                        <InputLabel>Municípios</InputLabel>
                        <Select
                            value={''}
                            onChange={(e) => {
                                setFieldValues({
                                    ...fieldValues,
                                    municipio: [].concat(fieldValues?.municipio ?? [], [e?.target?.value])
                                })

                                setMunicipiosSelecionados([
                                    ...municipiosSelecionados,
                                    municipiosOptions?.find((element) => element?.value === e?.target?.value)
                                ])
                            }}
                        >
                            {municipiosOptions
                                ?.filter((item) => item !== municipiosSelecionados?.find((mun) => mun?.value === item?.value))
                                ?.map((item, index) => {
                                    return (
                                        <MenuItem key={index} value={item?.value}>
                                            {item?.label}
                                        </MenuItem>
                                    )
                                })}
                        </Select>
                        {municipiosSelecionados?.length > 0 && (
                            <S.SelectedMunicipality>
                                {municipiosSelecionados?.map((item, index) => {
                                    return (
                                        <BadgeSelect
                                            key={index}
                                            deletarMunicipio={() => {
                                                setMunicipiosSelecionados(municipiosSelecionados?.filter((element) => element?.value !== item?.value))
                                                setFieldValues({
                                                    ...fieldValues,
                                                    municipio: fieldValues?.municipio?.filter((element) => element !== item?.value)
                                                })
                                            }}
                                            municipio={item}
                                        >
                                            {item?.label}
                                        </BadgeSelect>
                                    )
                                })}
                            </S.SelectedMunicipality>
                        )}
                    </FormControl>

                    <FormControl>
                        <InputLabel>Situação</InputLabel>
                        <Select
                            value={''}
                            onChange={(e) => {
                                setFieldValues({
                                    ...fieldValues,
                                    situacao: [].concat(fieldValues?.situacao ?? [], [e?.target?.value])
                                })

                                setSituacoesSelecionados([
                                    ...situacaoSelecionados,
                                    situacaoOptions?.find((element) => element?.value === e?.target?.value)
                                ])
                            }}
                        >
                            {situacaoOptions
                                ?.filter((item) => item !== situacaoSelecionados?.find((mun) => mun?.value === item?.value))
                                ?.map((item, index) => {
                                    return (
                                        <MenuItem key={index} value={item?.value}>
                                            {item?.label}
                                        </MenuItem>
                                    )
                                })}
                        </Select>
                        {situacaoSelecionados?.length > 0 && (
                            <S.SelectedMunicipality>
                                {situacaoSelecionados?.map((item, index) => {
                                    return (
                                        <BadgeSelect
                                            key={index}
                                            deletarMunicipio={() => {
                                                setSituacoesSelecionados(situacaoSelecionados?.filter((element) => element?.value !== item?.value))

                                                setFieldValues({
                                                    ...fieldValues,
                                                    situacao: fieldValues?.situacao?.filter((element) => element !== item?.value)
                                                })
                                            }}
                                            municipio={item}
                                        >
                                            {item?.label}
                                        </BadgeSelect>
                                    )
                                })}
                            </S.SelectedMunicipality>
                        )}
                    </FormControl>

                    <FormControl>
                        {/* <InputLabel>Prestador</InputLabel> */}
                        <AsyncSimpleSelect
                            loadOptions={loadOptionsPrestador}
                            value={''}
                            filterOption={(option) => !prestadoresOptionsSelecionados?.some((element) => element?.value === option?.value)}
                            label="Busque por nome ou CNPJ"
                            defaultOptions={false}
                            onChange={(e: typeOptions) => {
                                setFieldValues({
                                    ...fieldValues,
                                    prestador: [].concat(fieldValues?.prestador ?? [], [e?.value])
                                })

                                setPrestadoresOptionsSelecionados([...prestadoresOptionsSelecionados, e])
                            }}
                        />

                        {prestadoresOptionsSelecionados?.length > 0 && (
                            <S.SelectedMunicipality>
                                {prestadoresOptionsSelecionados?.map((item, index) => {
                                    return (
                                        <BadgeSelect
                                            key={index}
                                            deletarMunicipio={() => {
                                                setPrestadoresOptionsSelecionados(
                                                    prestadoresOptionsSelecionados?.filter((element) => element?.value !== item?.value)
                                                )
                                                setFieldValues({
                                                    ...fieldValues,
                                                    prestador: fieldValues?.prestador?.filter((element) => element?.toString() !== item?.value)
                                                })
                                            }}
                                            municipio={item}
                                        >
                                            {item?.label}
                                        </BadgeSelect>
                                    )
                                })}
                            </S.SelectedMunicipality>
                        )}
                    </FormControl>
                    {openFilter && (
                        <>
                            <FormControl>
                                <TextField
                                    fullWidth
                                    label="Data do pagamento do ISS - Início"
                                    type="date"
                                    value={fieldValues?.dataPagamentoIssInicial}
                                    onChange={(e) => {
                                        setFieldValues({
                                            ...fieldValues,
                                            dataPagamentoIssInicial: e.target.value
                                        })
                                    }}
                                />
                            </FormControl>
                            <FormControl>
                                <TextField
                                    style={{ width: '50%' }}
                                    label="Data do pagamento do ISS - Fim"
                                    type="date"
                                    value={fieldValues?.dataPagamentoIssFinal}
                                    onChange={(e) => {
                                        setFieldValues({
                                            ...fieldValues,
                                            dataPagamentoIssFinal: e.target.value
                                        })
                                    }}
                                />
                            </FormControl>
                        </>
                    )}
                </S.FilterRow0>
                <S.WrapperFilterButtons>
                    {openFilter ? (
                        <Button
                            color="primary"
                            startIcon={<ReactSVG src={'/contas/assets/imgs/mai-ic-close-blue.svg'} />}
                            variant="text"
                            onClick={() => {
                                setOpenFilter(false), LimparFiltro()
                            }}
                        >
                            Limpar busca
                        </Button>
                    ) : (
                        <Button
                            color="primary"
                            startIcon={<ReactSVG src={'/contas/assets/imgs/mai-ic-filter-blue.svg'} />}
                            variant="text"
                            onClick={() => setOpenFilter(true)}
                        >
                            Busca avançada
                        </Button>
                    )}

                    <Button
                        color="secondary"
                        startIcon={<ReactSVG src={'/contas/assets/imgs/mai-ic-search.svg'} />}
                        variant="contained"
                        onClick={() => {
                            // setPrestadoresRemovidos([])
                            // setPrestadoresSelecionados([])
                            // setSelecionarTodos(false)
                            currentStep === 0
                                ? listarPrestadores(0, fieldValues, prestadoresSelecionados, prestadoresRemovidos)
                                : loadDadosRelatorios(fieldValues)
                        }}
                    >
                        Buscar
                    </Button>
                </S.WrapperFilterButtons>
            </S.FilterBodyContainer>

            {loadingDados ? (
                <Box sx={{ display: 'flex', width: '100%', justifyContent: 'center', alignItems: 'center', height: '70vh' }}>
                    <CircularProgress />
                </Box>
            ) : (
                <>
                    {currentStep === 0 && (
                        <>
                            {prestadoresList?.length > 0 ? (
                                <>
                                    <S.WrapperPagination>
                                        <S.ListagemPrestadores>
                                            <p>Prestadores</p>
                                        </S.ListagemPrestadores>

                                        <S.CheckboxSelectAll>
                                            <label htmlFor="checkAll">
                                                <Checkbox
                                                    id="checkAll"
                                                    checked={selecionarTodos}
                                                    onChange={(event) => {
                                                        setSelecionarTodos(event?.target?.checked)
                                                        setPrestadoresSelecionados([])
                                                        setPrestadoresRemovidos([])
                                                        updatePage(0)
                                                    }}
                                                />
                                                <p>Selecionar todos</p>
                                            </label>
                                        </S.CheckboxSelectAll>

                                        <S.TableTitle className="table-header" customGridStyles={'0.3fr 2.5fr 1.5fr 1fr 1fr 1fr'}>
                                            <div></div>
                                            {titles.map((element) => (
                                                <div key={element.value}>{element.label}</div>
                                            ))}
                                        </S.TableTitle>

                                        <S.TableBody className="table-body" customGridStyles={'0.3fr 2.5fr 1.5fr 1fr 1fr 1fr'}>
                                            {prestadoresList?.map((element, index) => (
                                                <S.ListItem key={index} customGridStyles={'0.3fr 2.5fr 1.5fr 1fr 1fr 1fr'}>
                                                    <Checkbox
                                                        key={index}
                                                        checked={
                                                            selecionarTodos
                                                                ? !prestadoresRemovidos.includes(Number(element?.id))
                                                                : prestadoresSelecionados.includes(Number(element?.id))
                                                        }
                                                        onChange={(event) => {
                                                            if (selecionarTodos) {
                                                                event?.target?.checked
                                                                    ? setPrestadoresRemovidos(
                                                                          prestadoresRemovidos.filter((item) => item !== Number(element?.id))
                                                                      )
                                                                    : setPrestadoresRemovidos([...prestadoresRemovidos, Number(element?.id)])
                                                            } else {
                                                                event?.target?.checked
                                                                    ? setPrestadoresSelecionados([...prestadoresSelecionados, Number(element?.id)])
                                                                    : setPrestadoresSelecionados(
                                                                          prestadoresSelecionados.filter((item) => item !== Number(element?.id))
                                                                      )
                                                            }
                                                        }}
                                                    />
                                                    <S.CardInfo>
                                                        <p
                                                            className="blue_title link"
                                                            onClick={() =>
                                                                route.push('/recolhimento-iss/visualizar-recolhimento/' + Number(element?.id))
                                                            }
                                                        >
                                                            {element?.nome}
                                                        </p>
                                                        <h3>{element?.municipio}</h3>
                                                    </S.CardInfo>
                                                    {titles.slice(1).map((titulo, index) => (
                                                        <div key={index}>
                                                            <div>{element[titulo.value]}</div>
                                                        </div>
                                                    ))}
                                                </S.ListItem>
                                            ))}
                                        </S.TableBody>

                                        {pagination ? (
                                            <S.TableFooter>
                                                <Pagination
                                                    totalPage={pagination?.totalPaginas}
                                                    totalRegister={pagination?.totalRegistros}
                                                    actualPage={pagination?.paginaAtual}
                                                    setNumberPage={pagination?.setNumberPage}
                                                />
                                            </S.TableFooter>
                                        ) : null}
                                    </S.WrapperPagination>
                                </>
                            ) : (
                                <NoItensComponent
                                    src="/contas/assets/imgs/notfound.svg"
                                    titulo="Ops!... nenhum resultado encontrado"
                                    subTitulo="Tente outra pesquisa"
                                />
                            )}
                            <S.InfoValores>
                                <S.PrestadoresCount>
                                    <span className="info_card">Prestadores selecionados</span>
                                    <span className="info_main">
                                        {selecionarTodos ? totalElements - prestadoresRemovidos?.length : prestadoresSelecionados?.length}
                                    </span>
                                </S.PrestadoresCount>
                                <Button
                                    disabled={
                                        (prestadoresSelecionados?.length === 0 || prestadoresSelecionados?.length === undefined) && !selecionarTodos
                                    }
                                    color="secondary"
                                    // startIcon={<ReactSVG src={'/contas/assets/imgs/mai-ic-chevron-double-right-black.svg'} />}
                                    variant="contained"
                                    onClick={() => {
                                        //route.push('/recolhimento-iss/controle-recolhimento?filters=' + getJsonFiltersTelaControleRecolhimento())
                                        // goToControleRecolhimentos()

                                        handleClickToSearchBtn(fieldValues, prestadoresSelecionados, prestadoresRemovidos)
                                    }}
                                >
                                    Avançar
                                </Button>
                            </S.InfoValores>
                        </>
                    )}
                    {currentStep === 1 && (
                        <>
                            {relatoriosList?.length > 0 ? (
                                <>
                                    {relatoriosList?.map((item) => (
                                        <>
                                            <S.WrapperPagination>
                                                <S.WrapperTituloMunicipio>{item?.municipio}</S.WrapperTituloMunicipio>

                                                <S.TableTitle
                                                    className="table-header"
                                                    customGridStyles={'2fr 1fr 1.6fr 1.6fr 1fr 1fr 1fr 1fr 1fr 0.5fr'}
                                                >
                                                    {titlesRelatorios.map((element) => (
                                                        <div key={element.value}>{element.label}</div>
                                                    ))}
                                                </S.TableTitle>

                                                <S.TableBody
                                                    className="table-body"
                                                    customGridStyles={'2fr 1fr 1.6fr 1.6fr 1fr 1fr 1fr 1fr 1fr 0.3fr'}
                                                >
                                                    {item?.prestadores?.map((element, index) => (
                                                        <>
                                                            <S.ListItem
                                                                key={index}
                                                                customGridStyles={'2fr 1fr 1.6fr 1.6fr 1fr 1fr 1fr 1fr 1fr 0.5fr'}
                                                            >
                                                                <S.CardInfo>
                                                                    <h4>{element?.nome}</h4>
                                                                    <h3>{element?.cnpj}</h3>
                                                                </S.CardInfo>

                                                                <S.CardInfo>
                                                                    <h4>{element?.situacaoDescricao}</h4>
                                                                    <h3>{element?.bancoPagador}</h3>
                                                                </S.CardInfo>

                                                                {titlesRelatorios.slice(2).map((titulo, index) => (
                                                                    <div key={index}>
                                                                        <div>{element?.[titulo?.value]}</div>
                                                                    </div>
                                                                ))}

                                                                <div
                                                                    style={{ textAlign: 'center', cursor: 'pointer' }}
                                                                    onClick={() =>
                                                                        route.push(`/recolhimento-iss/controle-recolhimento?pcId=${element?.pcId}`)
                                                                    }
                                                                >
                                                                    <ReactSVG src="/contas/assets/imgs/pen.svg" />
                                                                </div>
                                                            </S.ListItem>
                                                        </>
                                                    ))}
                                                </S.TableBody>

                                                <S.TableBody>
                                                    <S.ListItem customGridStyles={'7fr 1fr 1fr 1fr 1.5fr'}>
                                                        <S.WrapperTituloMunicipio>Totais</S.WrapperTituloMunicipio>
                                                        {titlesSomatorioRelatorios.map((titulo, index) => (
                                                            <div key={index}>
                                                                <S.CardInfo style={{ gap: '14px' }}>
                                                                    <h4>{titulo.label}</h4>
                                                                    <p className="blue_title">{item?.totais?.[titulo?.value]}</p>
                                                                </S.CardInfo>
                                                            </div>
                                                        ))}
                                                    </S.ListItem>
                                                </S.TableBody>
                                            </S.WrapperPagination>
                                        </>
                                    ))}

                                    <S.WrapperButton>
                                        <Button color="secondary" variant="contained" onClick={() => setModal(true)}>
                                            Exportar relatório
                                        </Button>
                                    </S.WrapperButton>
                                </>
                            ) : (
                                <NoItensComponent
                                    src="/contas/assets/imgs/notfound.svg"
                                    titulo="Ops!... nenhum resultado encontrado"
                                    subTitulo="Tente outra pesquisa"
                                />
                            )}
                        </>
                    )}
                </>
            )}
            <Modal title="Exportar relatório de controle de recolhimento de ISS" isOpen={modal} onClose={() => setModal(false)} closeButton={false}>
                <div>Escolha um modelo de exportação</div>
                <S.ModalContent>
                    <FormControl>
                        <RadioGroup
                            row
                            name="row-radio-buttons-group"
                            value={fileType}
                            onChange={(e) => setFileType(e?.target?.value === 'pdf' ? 'pdf' : 'excel')}
                        >
                            <FormControlLabel value="pdf" control={<Radio />} label="PDF" />
                            <FormControlLabel value="excel" control={<Radio />} label="Excel" />
                        </RadioGroup>
                    </FormControl>
                </S.ModalContent>
                <S.WrapperFilterButtons>
                    <Button
                        color="neutral"
                        variant="text"
                        onClick={() => {
                            setModal(false)
                            setFileType('pdf')
                        }}
                    >
                        Cancelar
                    </Button>
                    <Button
                        color="secondary"
                        variant="contained"
                        onClick={() => {
                            downloadRelatorio(fieldValues, fileType)
                            setFileType('pdf')
                            setModal(false)
                        }}
                    >
                        Exportar
                    </Button>
                </S.WrapperFilterButtons>
            </Modal>
        </S.BodyContainer>
    )
}

export default SelecionarPrestadoresRecolhimentoISSTemplate
