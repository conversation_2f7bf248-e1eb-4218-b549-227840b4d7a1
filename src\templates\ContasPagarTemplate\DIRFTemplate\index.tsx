import { Box, Button, CircularProgress, FormControl, InputLabel, MenuItem, Select } from '@mui/material'
import AsyncSimpleSelect from 'components/atoms/AsyncSimpleSelect'
import NoItensComponent from 'components/atoms/NoItensComponent'
import TablePagination from 'components/molecules/TablePagination'
import { useFilterPersistence } from 'hooks/storage/useFilterPersistence'
import { useToast } from 'hooks/toast'
import { useEffect, useState } from 'react'
import { ReactSVG } from 'react-svg'
import { Dirf } from 'src/services/ContasPagar/dirf-controller'
import { IDirfDto, IDirfRendimentosRetencoesDto } from 'src/services/ContasPagar/dirf-controller/types'
import { Prestador } from 'src/services/ContasPagar/prestador-controller'
import { IBuscaPrestadorDto } from 'src/services/ContasPagar/prestador-controller/prestador-controller-type'
import { downloadFileV2 } from 'utils/functions'
import { ISelectProps } from '../RelatoriosTemplate/FitroHistoricoIntegracao'
import * as S from './styles'

export type filterType = {
    ano: number
    prestadorUUID?: string
}

function DIRFTemplate() {
    const { addToast } = useToast()

    // Hook para persistência dos filtros
    const {
        filters: persistedFilters,
        updateFilters: updatePersistedFilters,
        clearFilters: clearPersistedFilters
    } = useFilterPersistence({
        route: '/DIRF',
        filterType: 'dirf',
        defaultValues: {
            ano: undefined,
            prestadorUUID: ''
        }
    })

    const [loadingData, setLoadingData] = useState(false)
    const [prestador, setPrestador] = useState<ISelectProps>(() => {
        if (persistedFilters?.prestadorUUID) {
            return { value: persistedFilters.prestadorUUID, label: 'Carregando...', Element: {} as IBuscaPrestadorDto }
        }
        return {} as ISelectProps
    })
    const [dataFilter, setDataFilter] = useState<filterType>(() => {
        if (persistedFilters && Object.keys(persistedFilters).length > 0) {
            return persistedFilters as filterType
        }
        return undefined
    })
    const [anosOptions, setAnosOptions] = useState<number[]>([])
    const [dirfInfo, setDirfInfo] = useState<IDirfDto>()
    const [imprimirBtnDisabled, setImprimirBtnDisabled] = useState(false)

    const loadOptionsPrestador = async (str: string): Promise<ISelectProps<IBuscaPrestadorDto>[]> => {
        return Prestador.getPorNomeCnpj({ nomeCNPJ: str }).then(({ data }) =>
            data.content.map((item) => ({ value: item.prestadorId, label: item.nome, Element: item }))
        )
    }

    const loadPrestadorByUUID = async (uuid: string) => {
        try {
            const { data } = await Prestador.getPorNomeCnpj({ nomeCNPJ: '', size: 1000 })
            const prestadorEncontrado = data.content.find((item) => item.prestadorId === uuid)
            if (prestadorEncontrado) {
                setPrestador({
                    value: prestadorEncontrado.prestadorId,
                    label: prestadorEncontrado.nome,
                    Element: prestadorEncontrado
                } as ISelectProps)
            } else {
                setPrestador({} as ISelectProps)
            }
        } catch (error) {
            console.error('Erro ao carregar prestador:', error)
            setPrestador({} as ISelectProps)
        }
    }

    const titles: { label: string; value: keyof IDirfRendimentosRetencoesDto | 'acao' }[] = [
        { label: 'Mês', value: 'mes' },
        { label: 'Código de retenção', value: 'codigoRetencao' },
        { label: 'Descrição do Rendimento', value: 'descricaoRendimento' },
        { label: 'Rendimento', value: 'rendimento' },
        { label: 'Imposto retido ', value: 'impostoRetido' }
        // { label: '', value: 'acao' }
    ]

    function getAnos() {
        Dirf.getDirfAnos().then(({ data }) => {
            setAnosOptions(data)
            if (data?.length > 0) {
                const anoPadrao = data[0]
                setDataFilter((prev) => ({ ...prev, ano: anoPadrao }))
                if (!persistedFilters?.ano) {
                    updatePersistedFilters({ ano: anoPadrao })
                }
            }
        })
    }

    function getInfoDirf() {
        if (dataFilter && Object.keys(dataFilter).length > 0) {
            updatePersistedFilters(dataFilter)
        }

        setLoadingData(true)
        Dirf.getDirf({ ano: dataFilter?.ano, prestadorId: dataFilter?.prestadorUUID })
            .then(({ data }) => {
                setDirfInfo(data)
            })
            .catch((err) => {
                addToast({
                    title: err?.response?.data?.message ? err?.response?.data?.message : 'Ocorreu um erro: Desconhecido',
                    type: 'error',
                    duration: 5000
                })
            })
            .finally(() => setLoadingData(false))
    }

    const handleLimparFiltros = () => {
        const anoAtual = dataFilter?.ano
        setDataFilter(anoAtual ? { ano: anoAtual } : undefined)
        setPrestador({} as ISelectProps)
        setDirfInfo(undefined)

        if (anoAtual) {
            updatePersistedFilters({ ano: anoAtual })
        } else {
            clearPersistedFilters()
        }
    }
    function getPdf() {
        setImprimirBtnDisabled(true)
        Dirf.getDirfPdf({ ano: dataFilter?.ano, prestadorId: dataFilter?.prestadorUUID })
            .then((response) => {
                downloadFileV2(response.data, `DIRF_${prestador?.label}_${dataFilter?.ano}`)
            })
            .catch((err) => {
                addToast({
                    title: err?.response?.data?.message ? err?.response?.data?.message : 'Ocorreu um erro: Desconhecido',
                    type: 'error',
                    duration: 5000
                })
            })
            .finally(() => setImprimirBtnDisabled(false))
    }

    useEffect(() => {
        getAnos()
    }, [])

    useEffect(() => {
        if (persistedFilters?.prestadorUUID && prestador.label === 'Carregando...') {
            loadPrestadorByUUID(persistedFilters.prestadorUUID)
        }
    }, [persistedFilters?.prestadorUUID])

    useEffect(() => {
        if (
            persistedFilters?.ano &&
            persistedFilters?.prestadorUUID &&
            prestador.value === persistedFilters.prestadorUUID &&
            prestador.label !== 'Carregando...' &&
            !dirfInfo
        ) {
            getInfoDirf()
        }
    }, [persistedFilters, prestador.value, prestador.label, dirfInfo])

    return (
        <S.Container>
            <S.FilterContainer>
                <div className="row_0">
                    <FormControl key={dataFilter?.ano}>
                        <InputLabel>Ano Calendário</InputLabel>
                        <Select
                            value={dataFilter?.ano}
                            // value={dataFilter?.competencia}

                            onChange={(e) => {
                                setDataFilter({
                                    ...dataFilter,
                                    ano: Number(e?.target?.value)
                                })
                            }}
                        >
                            {anosOptions.map((item, index) => {
                                return (
                                    <MenuItem key={index} value={item}>
                                        {item}
                                    </MenuItem>
                                )
                            })}
                        </Select>
                    </FormControl>
                    <AsyncSimpleSelect
                        label="Prestador"
                        value={prestador}
                        defaultValue={() => prestador}
                        onChange={(e) => {
                            setPrestador(e)
                            setDataFilter({ ...dataFilter, prestadorUUID: e?.value })
                        }}
                        isClearable
                        loadOptions={loadOptionsPrestador}
                    />
                </div>
                <div style={{ marginLeft: 'auto', display: 'flex', gap: '8px' }}>
                    {dataFilter?.prestadorUUID && (
                        <Button variant="outlined" color="secondary" onClick={handleLimparFiltros}>
                            Limpar Filtros
                        </Button>
                    )}
                    <Button
                        onClick={() => getInfoDirf()}
                        disabled={!dataFilter?.ano || !dataFilter?.prestadorUUID}
                        startIcon={<ReactSVG src="/contas/assets/imgs/mai-ic-search.svg" />}
                        color="secondary"
                    >
                        Buscar
                    </Button>
                </div>
            </S.FilterContainer>

            <>
                {loadingData ? (
                    <Box sx={{ display: 'flex', width: '100%', justifyContent: 'center', alignItems: 'center', height: '70vh' }}>
                        <CircularProgress />
                    </Box>
                ) : (
                    <>
                        {dirfInfo && (
                            <>
                                <S.InfoContainer>
                                    <h3>Fonte Pagadora</h3>
                                    <div className="row">
                                        <S.CardInfo>
                                            <span className="title_info">Nome Empresarial</span>
                                            <h3 className="info_principal">
                                                {dirfInfo?.fontePagadora?.nomeEmpresarial ? dirfInfo?.fontePagadora?.nomeEmpresarial : '---'}
                                            </h3>
                                        </S.CardInfo>
                                        <S.CardInfo>
                                            <span className="title_info">CNPJ</span>
                                            <h3 className="info_principal">
                                                {dirfInfo?.fontePagadora?.cnpj ? dirfInfo?.fontePagadora?.cnpj : '---'}
                                            </h3>
                                        </S.CardInfo>
                                    </div>
                                    <h3>Pessoa Jurídica Beneficiária dos Rendimentos </h3>
                                    <div className="row">
                                        <S.CardInfo>
                                            <span className="title_info">Nome Empresarial</span>
                                            <h3 className="info_principal">
                                                {dirfInfo?.beneficiario?.nomeEmpresarial ? dirfInfo?.beneficiario?.nomeEmpresarial : '---'}
                                            </h3>
                                        </S.CardInfo>
                                        <S.CardInfo>
                                            <span className="title_info">CNPJ</span>
                                            <h3 className="info_principal">{dirfInfo?.beneficiario?.cnpj ? dirfInfo?.beneficiario?.cnpj : '---'}</h3>
                                        </S.CardInfo>
                                    </div>
                                </S.InfoContainer>
                                <S.ListContainer>
                                    <h3>Rendimento e Imposto Retido na Fonte</h3>
                                    <>
                                        {dirfInfo?.rendimentosRetencoes?.length > 0 ? (
                                            <>
                                                <div>
                                                    <TablePagination
                                                        titles={titles}
                                                        values={dirfInfo?.rendimentosRetencoes?.map((item) => {
                                                            return {
                                                                ...item
                                                            }
                                                        })}
                                                        customGridStyles="0.8fr 1fr 3fr 1.2fr 0.8fr 0.1fr"
                                                        noSelectAll={true}
                                                    />
                                                </div>
                                            </>
                                        ) : (
                                            <NoItensComponent
                                                src="/contas/assets/imgs/notfound.svg"
                                                titulo="Ops!... nenhum resultado encontrado"
                                                subTitulo="Tente outra pesquisa"
                                            />
                                        )}
                                    </>
                                </S.ListContainer>
                                <Button
                                    sx={{ width: 'fit-content', marginLeft: 'auto' }}
                                    disabled={imprimirBtnDisabled}
                                    onClick={() => getPdf()}
                                    color="secondary"
                                >
                                    Imprimir
                                </Button>
                            </>
                        )}
                    </>
                )}
            </>
        </S.Container>
    )
}

export default DIRFTemplate
