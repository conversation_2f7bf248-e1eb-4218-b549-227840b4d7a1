import { Box, CircularProgress, FormControl, InputLabel, MenuItem, Select, TextField } from '@mui/material'
import { Dispatch, SetStateAction, useEffect, useState } from 'react'
import { ReactSVG } from 'react-svg'
import { IDataFilterHistoricoIntegracao } from '../filtro-types'

import { Competencia } from 'src/services/ContasPagar/competencia-controller'
import { HistoricoIntegracao } from 'src/services/ContasPagar/historico-integracao-controller'
import { TipoArquivoEnum } from 'src/services/ContasPagar/historico-integracao-controller/enuns'
import { parseDataToMMYYYY } from 'utils/functions'
import { selectSituacaoArquivoOptions, selectTipoRetornoOptions } from './mocksSelec'
import * as S from './styles'

type FiltroHistoricoIntegracao = {
    dataFilter: IDataFilterHistoricoIntegracao
    setDataFilter: Dispatch<SetStateAction<IDataFilterHistoricoIntegracao>>
    buscar: (data?: string) => void
}
type typeOptions = {
    label: string
    value: string
}
const FiltroHistoricoIntegracao = ({ dataFilter, setDataFilter, buscar }: FiltroHistoricoIntegracao) => {
    const [competenciaOptions, setCompetenciaOptions] = useState<typeOptions[]>([])
    const [loadingFilter, setLoadingFilter] = useState(true)
    const [tiposArquivo, setTiposArquivo] = useState<
        {
            value: TipoArquivoEnum
            description: string
        }[]
    >()

    const [localFilters, setLocalFilters] = useState<IDataFilterHistoricoIntegracao>(dataFilter)
    const [defaultCompetencia, setDefaultCompetencia] = useState<string>('')

    function getCompetencias() {
        Competencia.get().then(({ data }) => {
            const parseCompetenciaOptions: typeOptions[] = data?.map((item) => ({
                label: parseDataToMMYYYY(item?.competencia?.toString(), 'MM/YYYY'),
                value: item?.competencia?.toString()
            }))
            setCompetenciaOptions(parseCompetenciaOptions)

            const competenciaPadrao = data?.[data?.length > 0 ? data?.length - 1 : 0]?.competencia?.toString()
            setDefaultCompetencia(competenciaPadrao || '')

            if (!localFilters?.competencia) {
                setLocalFilters((prev) => ({ ...prev, competencia: competenciaPadrao }))
                setDataFilter((prev) => ({ ...prev, competencia: competenciaPadrao }))
                buscar(competenciaPadrao)
            }
            setLoadingFilter(false)
        })
    }

    function getTiposArquivo() {
        HistoricoIntegracao.getTiposArquivo()
            .then(({ data }) => {
                setTiposArquivo(data)
            })
            .catch((err) => console.log(err))
    }

    useEffect(() => {
        setLocalFilters(dataFilter)
    }, [dataFilter])

    useEffect(() => {
        if (!dataFilter?.competencia && defaultCompetencia) {
            setLocalFilters((prev) => ({ ...prev, competencia: defaultCompetencia }))
            setDataFilter((prev) => ({ ...prev, competencia: defaultCompetencia }))
            buscar(defaultCompetencia)
        }
    }, [dataFilter?.competencia, defaultCompetencia, setDataFilter, buscar])

    useEffect(() => {
        getTiposArquivo()
        getCompetencias()
    }, [])

    const handleBuscar = () => {
        setDataFilter(localFilters)
        buscar()
    }

    return (
        <>
            {loadingFilter ? (
                <Box sx={{ display: 'flex', width: '100%', justifyContent: 'center', alignItems: 'center', height: '70vh' }}>
                    <CircularProgress />
                </Box>
            ) : (
                <S.Container>
                    <S.BodyContainer>
                        <S.FilterRow0>
                            <FormControl>
                                <InputLabel required>Competência</InputLabel>
                                <Select
                                    value={localFilters?.competencia || defaultCompetencia || ''}
                                    onChange={(e) => {
                                        setLocalFilters((prev) => ({
                                            ...prev,
                                            competencia: e?.target?.value
                                        }))
                                    }}
                                >
                                    {competenciaOptions?.map((item, index) => {
                                        return (
                                            <MenuItem key={index} value={item?.value}>
                                                {item?.label}
                                            </MenuItem>
                                        )
                                    })}
                                </Select>
                            </FormControl>
                            <S.SearchWrapper>
                                <TextField
                                    label="Prestador"
                                    fullWidth
                                    value={localFilters?.prestador || ''}
                                    onChange={({ target: { value } }) => {
                                        setLocalFilters((prev) => ({
                                            ...prev,
                                            prestador: value?.replace(/[^\d\w\W]+/g, '')
                                        }))
                                    }}
                                />
                            </S.SearchWrapper>
                        </S.FilterRow0>
                        <S.FilterRow1>
                            <FormControl>
                                <InputLabel required>Situação do Arquivo</InputLabel>
                                <Select
                                    value={localFilters?.situacaoArquivo || 'null'}
                                    onChange={(e) => {
                                        setLocalFilters((prev) => ({
                                            ...prev,
                                            situacaoArquivo: e?.target?.value
                                        }))
                                    }}
                                >
                                    {selectSituacaoArquivoOptions?.map((item, index) => {
                                        return (
                                            <MenuItem key={index} value={item?.value}>
                                                {item?.label}
                                            </MenuItem>
                                        )
                                    })}
                                </Select>
                            </FormControl>
                            <FormControl>
                                <InputLabel required>Tipo de Arquivo</InputLabel>
                                <Select
                                    value={localFilters?.tipoArquivo || 'null'}
                                    onChange={(e) => {
                                        setLocalFilters((prev) => ({
                                            ...prev,
                                            tipoArquivo: e?.target?.value
                                        }))
                                    }}
                                >
                                    <MenuItem value={'null'}>{'Todos'}</MenuItem>
                                    {tiposArquivo?.map((item, index) => {
                                        return (
                                            <MenuItem key={index} value={item?.value}>
                                                {item?.description}
                                            </MenuItem>
                                        )
                                    })}
                                </Select>
                            </FormControl>

                            <FormControl>
                                <InputLabel required>Tipo de Retorno</InputLabel>
                                <Select
                                    value={localFilters?.tipoRetorno || 'null'}
                                    onChange={(e) => {
                                        setLocalFilters((prev) => ({
                                            ...prev,
                                            tipoRetorno: e?.target?.value
                                        }))
                                    }}
                                >
                                    {selectTipoRetornoOptions?.map((item, index) => {
                                        return (
                                            <MenuItem key={index} value={item?.value}>
                                                {item?.label}
                                            </MenuItem>
                                        )
                                    })}
                                </Select>
                            </FormControl>
                        </S.FilterRow1>
                    </S.BodyContainer>
                    <S.IconSearchWrapper onClick={handleBuscar}>
                        <ReactSVG src="/contas/assets/imgs/mai-ic-search.svg" />
                    </S.IconSearchWrapper>
                </S.Container>
            )}
        </>
    )
}

export default FiltroHistoricoIntegracao
