import { FormControl, TextField } from '@mui/material'
import { Dispatch, SetStateAction, useEffect, useState } from 'react'
import { ReactSVG } from 'react-svg'

import { DatePicker, LocalizationProvider } from '@mui/x-date-pickers'
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns'
import SimpleSelect from 'components/molecules/SimpleSelect'
import ptBR from 'date-fns/locale/pt-BR'
import { IGetDescontosFinanceiros } from 'src/services/ContasPagar/descontos-financeiros'
import { SituacaoDescontoEnum } from 'src/services/ContasPagar/descontos-financeiros/desconstosFinanceirosEnum'
import * as S from './styles'

type FitroDescontoFinanceiro = {
    dataFilter?: IGetDescontosFinanceiros
    setDataFilter?: Dispatch<SetStateAction<IGetDescontosFinanceiros>>
    competenciaLocal?: Date | null
    setCompetenciaLocal?: Dispatch<SetStateAction<Date | null>>
    buscar: (data: IGetDescontosFinanceiros) => void
}

const SituacaoOptions = Object.keys(SituacaoDescontoEnum).map((item) => ({ value: item, label: item }))

const DefaultFilterValues: IGetDescontosFinanceiros = {
    competencia: null,
    nomeCNPJCodigo: '',
    situacao: null
}

const FitroDescontoFinanceiro = ({ dataFilter, setDataFilter, competenciaLocal, setCompetenciaLocal, buscar }: FitroDescontoFinanceiro) => {
    const [localDataFilter, setLocalDataFilter] = useState<IGetDescontosFinanceiros>(DefaultFilterValues)

    useEffect(() => {
        if (dataFilter && Object.keys(dataFilter).length > 0) {
            setLocalDataFilter(dataFilter)
        } else {
            setLocalDataFilter(DefaultFilterValues)
        }
    }, [dataFilter])

    useEffect(() => {
        if (!dataFilter || Object.keys(dataFilter).length === 0) {
            if (setCompetenciaLocal) {
                setCompetenciaLocal(null)
            }
        }
    }, [dataFilter, setCompetenciaLocal])

    function handleSearch() {
        // Garantir que a data sempre seja dia 1
        let competenciaDate = null
        if (competenciaLocal) {
            // Usar competenciaLocal diretamente
            competenciaDate = `${competenciaLocal.getFullYear()}-${String(competenciaLocal.getMonth() + 1).padStart(2, '0')}-01`
        }

        const dataSearch: IGetDescontosFinanceiros = {
            nomeCNPJCodigo: localDataFilter?.nomeCNPJCodigo,
            situacao: localDataFilter?.situacao,
            competencia: competenciaDate
        }

        if (setDataFilter) {
            setDataFilter(dataSearch)
        }
        buscar(dataSearch)
    }

    return (
        <S.FilterRow0>
            <FormControl>
                <LocalizationProvider dateAdapter={AdapterDateFns} locale={ptBR}>
                    <DatePicker
                        label={'Competência'}
                        views={['month', 'year']}
                        value={competenciaLocal}
                        onChange={(newValue) => {
                            if (setCompetenciaLocal) {
                                setCompetenciaLocal(newValue)
                            }
                        }}
                        renderInput={(params) => {
                            return (
                                <TextField
                                    {...params}
                                    inputProps={{
                                        ...params.inputProps,
                                        placeholder: 'Mês / Ano'
                                    }}
                                />
                            )
                        }}
                    />
                </LocalizationProvider>
            </FormControl>

            <FormControl>
                <SimpleSelect
                    defaultValue={() => ''}
                    isClearable
                    label="Situação"
                    value={localDataFilter?.situacao ? SituacaoOptions.find((item) => item.value === localDataFilter.situacao) : null}
                    onChange={(e) => {
                        // Salvar o valor direto do select, não o enum
                        setLocalDataFilter({
                            ...localDataFilter,
                            situacao: e?.value || null
                        })
                    }}
                    options={SituacaoOptions}
                />
            </FormControl>
            <S.SearchWrapper>
                <FormControl>
                    <TextField
                        value={localDataFilter?.nomeCNPJCodigo || ''}
                        label="Prestador"
                        placeholder="Pesquise por nome, CNPJ ou código"
                        onChange={(e) => {
                            setLocalDataFilter({ ...localDataFilter, nomeCNPJCodigo: e?.target?.value?.trim() })
                        }}
                    />
                </FormControl>
                <S.IconSearchWrapper onClick={handleSearch}>
                    <ReactSVG src="/contas/assets/imgs/mai-ic-search.svg" />
                </S.IconSearchWrapper>
            </S.SearchWrapper>
        </S.FilterRow0>
    )
}

export default FitroDescontoFinanceiro

