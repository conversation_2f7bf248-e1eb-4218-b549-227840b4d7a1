import { Box, CircularProgress, FormControl, InputLabel, MenuItem, Select, TextField } from '@mui/material'
import { Dispatch, SetStateAction, useEffect, useState } from 'react'
import { ReactSVG } from 'react-svg'
import { IDataFilterAcompanhamento } from '../filtro-types'

import { Competencia } from 'src/services/ContasPagar/competencia-controller'
import { parseDataToMMYYYY } from 'utils/functions'
import * as S from './styles'

type FitroAcompanhamentos = {
    dataFilter: IDataFilterAcompanhamento
    setDataFilter: Dispatch<SetStateAction<IDataFilterAcompanhamento>>
    buscar: (data?: string) => void
}

type typeOptions = {
    label: string
    value: string
}

const FitroAcompanhamentos = ({ dataFilter, setDataFilter, buscar }: FitroAcompanhamentos) => {
    const [competenciaOptions, setCompetenciaOptions] = useState<typeOptions[]>([])
    const [loadingFilter, setLoadingFilter] = useState(true)
    const [defaultCompetencia, setDefaultCompetencia] = useState<string>('')

    function getCompetencias() {
        Competencia.get().then(({ data }) => {
            const parseCompetenciaOptions: typeOptions[] = data?.map((item) => ({
                label: parseDataToMMYYYY(item?.competencia?.toString(), 'MM/YYYY'),
                value: item?.competencia?.toString()
            }))
            setCompetenciaOptions(parseCompetenciaOptions)

            const competenciaPadrao = data?.[data?.length > 0 ? data?.length - 1 : 0]?.competencia?.toString()
            setDefaultCompetencia(competenciaPadrao || '')

            if (!dataFilter?.competencia) {
                setDataFilter((prev) => ({ ...prev, competencia: competenciaPadrao }))
                buscar(competenciaPadrao)
            }
            setLoadingFilter(false)
        })
    }

    useEffect(() => {
        if (!dataFilter?.competencia && defaultCompetencia) {
            setDataFilter((prev) => ({ ...prev, competencia: defaultCompetencia }))
            buscar(defaultCompetencia)
        }
    }, [dataFilter?.competencia, defaultCompetencia, setDataFilter, buscar])

    useEffect(() => {
        getCompetencias()
    }, [])

    return (
        <>
            {loadingFilter ? (
                <Box sx={{ display: 'flex', width: '100%', justifyContent: 'center', alignItems: 'center', height: '70vh' }}>
                    <CircularProgress />
                </Box>
            ) : (
                <S.BodyContainer>
                    <S.FilterRow0>
                        <FormControl>
                            <InputLabel required>Competência</InputLabel>
                            <Select
                                value={dataFilter?.competencia || defaultCompetencia || ''}
                                onChange={(e) => {
                                    setDataFilter({
                                        ...dataFilter,
                                        competencia: e?.target?.value
                                    })
                                }}
                            >
                                {competenciaOptions?.map((item, index) => {
                                    return (
                                        <MenuItem key={index} value={item?.value}>
                                            {item?.label}
                                        </MenuItem>
                                    )
                                })}
                            </Select>
                        </FormControl>

                        <S.SearchWrapper>
                            {/* <PrestadorInput handleOnChange={(e) => setDataFilter({ ...dataFilter, prestadorUUID: e?.uuid })} /> */}
                            <TextField
                                label="Prestador"
                                fullWidth
                                value={dataFilter?.prestador || ''}
                                onChange={({ target: { value } }) => {
                                    setDataFilter({ ...dataFilter, prestador: value?.replace(/[^\d\w\W]+/g, '') })
                                }}
                            />
                            <S.IconSearchWrapper onClick={() => buscar()}>
                                <ReactSVG src="/contas/assets/imgs/mai-ic-search.svg" />
                            </S.IconSearchWrapper>
                        </S.SearchWrapper>
                    </S.FilterRow0>
                </S.BodyContainer>
            )}
        </>
    )
}

export default FitroAcompanhamentos
