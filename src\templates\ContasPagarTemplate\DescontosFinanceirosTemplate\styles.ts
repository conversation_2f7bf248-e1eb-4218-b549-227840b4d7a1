import styled from 'styled-components'

export const BodyContainer = styled.div`
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 32px;
`
export const ListagemPrestadores = styled.div`
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24px;
    background: #ffffff;
    border-radius: 8px 8px 0px 0px;

    p {
        font-family: 'Open Sans';
        font-style: normal;
        font-weight: 600;
        font-size: 16px;
        line-height: 24px;
        text-transform: capitalize;
        color: #2b45d4;
    }
`
export const ActionWrapper = styled.div`
    width: 100%;
    display: flex;
    gap: 16px;
`
export const ActionButton = styled.div`
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 8px 6px 0px;
    gap: 10px;

    width: 30px;
    height: 30px;

    background: #e6e9fa;
    border-radius: 20px;
    cursor: pointer;
`
export const ListagemDestaque = styled.p`
    font-family: 'Open Sans';
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 24px;
    color: rgba(0, 0, 0, 0.88);
`

export const Wrapper = styled.div`
    width: 100%;
    background: #ffffff;
    padding: 25px;
    border-radius: 8px;
`

