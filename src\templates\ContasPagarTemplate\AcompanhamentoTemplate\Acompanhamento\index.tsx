import NoItensComponent from 'components/atoms/NoItensComponent'
import TablePagination from 'components/molecules/TablePagination'
import { useEffect, useState } from 'react'
import { ReactSVG } from 'react-svg'
import { IPagination } from 'src/types/pagination'
import { PaginationHelper } from 'utils/pagination-helper'
import { IDataFilterAcompanhamento } from '../Filtros/filtro-types'
import FitroAcompanhamentos from '../Filtros/FitroAcompanhamentos'
import * as S from './styles'

import { Box, Button, CircularProgress } from '@mui/material'
import { useFilterPersistence } from 'hooks/storage/useFilterPersistence'
import { useToast } from 'hooks/toast'
import { useRouter } from 'next/router'
import { Acompanhamento } from 'src/services/ContasPagar/acompanhamento-controller'
import { IGetAcompanhamento } from 'src/services/ContasPagar/acompanhamento-controller/acompanhamento-type'
import { maskCNPJ } from 'utils/masks/formatCNPJ'
import ExportarAcompanhamentoModal from '../modais/ModalExportarAcompanhamento'

export type filtroSelecionadoType = {
    competencia: string
    nomeCNPJ?: string
}

interface IPersistedFilters extends IDataFilterAcompanhamento {
    numberPage: number
}

const AcompanhamentoComponent = () => {
    const route = useRouter()
    const { addToast } = useToast()

    const {
        filters: persistedFilters,
        updateFilters: updatePersistedFilters,
        clearFilters: clearPersistedFilters
    } = useFilterPersistence({
        route: '/acompanhamento',
        filterType: 'acompanhamento',
        defaultValues: {
            competencia: '',
            prestador: '',
            numberPage: 0
        }
    })

    const typedPersistedFilters = persistedFilters as IPersistedFilters | null

    const [loadingDados, setLoadingDados] = useState(true)
    const [dataFilterAcompanhamento, setDataFilterAcompanhamento] = useState<IDataFilterAcompanhamento>(() => {
        if (typedPersistedFilters && Object.keys(typedPersistedFilters).length > 0) {
            return typedPersistedFilters
        }
        return {}
    })
    const [pagination, setPagination] = useState<IPagination>()
    const [numberPage, setNumberPage] = useState<number>(typedPersistedFilters?.numberPage || 0)
    const [acompanhamentoList, setAcompanhamentoList] = useState<IGetAcompanhamento[]>([])
    const [openModal, setOpenModal] = useState(false)
    const [filtroSelecionado, setFiltroSelecionado] = useState<filtroSelecionadoType>()

    const updatePage = (newPage: number) => {
        setNumberPage(newPage)
        if (dataFilterAcompanhamento && Object.keys(dataFilterAcompanhamento).length > 0) {
            updatePersistedFilters({
                ...dataFilterAcompanhamento,
                numberPage: newPage
            })
        }
    }

    function loadDados(competenciaInicial?: string) {
        setLoadingDados(true)

        const regexCnpj = /^\d{2}\.\d{3}\.\d{3}\/\d{4}-\d{2}$/
        const isCnpj = regexCnpj.test(dataFilterAcompanhamento?.prestador)
        setFiltroSelecionado({
            competencia: competenciaInicial ? competenciaInicial : dataFilterAcompanhamento?.competencia,
            nomeCNPJ: isCnpj ? dataFilterAcompanhamento?.prestador?.replace(/[^\d]+/g, '') : dataFilterAcompanhamento?.prestador
        })

        Acompanhamento.get({
            competencia: competenciaInicial ? competenciaInicial : dataFilterAcompanhamento?.competencia,
            nomeCNPJ: isCnpj ? dataFilterAcompanhamento?.prestador?.replace(/[^\d]+/g, '') : dataFilterAcompanhamento?.prestador,
            page: numberPage,
            size: 10
        })
            .then(({ data }) => {
                setAcompanhamentoList(data?.content)
                setPagination(PaginationHelper.parserPagination(data, updatePage))
            })
            .catch((err) => {
                addToast({
                    title: err?.response?.data?.message ? err?.response?.data?.message : 'Ocorreu um erro: Desconhecido',
                    type: 'error',
                    duration: 5000
                })
            })
            .finally(() => setLoadingDados(false))
    }

    const handleLimparFiltros = () => {
        const competenciaAtual = dataFilterAcompanhamento?.competencia
        const filtrosLimpos = { competencia: competenciaAtual }

        setDataFilterAcompanhamento(filtrosLimpos)
        setNumberPage(0)
        setAcompanhamentoList([])

        clearPersistedFilters()

        try {
            const key = '@filters:/acompanhamento:acompanhamento'
            sessionStorage.removeItem(key)
        } catch (error) {
            console.error('Erro ao limpar sessionStorage:', error)
        }

        Acompanhamento.get({
            competencia: competenciaAtual || '',
            nomeCNPJ: '',
            page: 0,
            size: 10
        })
            .then(({ data }) => {
                setAcompanhamentoList(data?.content)
                setPagination(PaginationHelper.parserPagination(data, updatePage))
            })
            .catch((err) => {
                addToast({
                    title: err?.response?.data?.message ? err?.response?.data?.message : 'Ocorreu um erro: Desconhecido',
                    type: 'error',
                    duration: 5000
                })
            })
    }

    // keyof IGetListagemPrestadores
    const titles: { label: string; value: keyof IGetAcompanhamento | 'acao' }[] = [
        { label: 'Prestador', value: 'nomePrestador' },
        { label: 'Competência', value: 'competencia' },
        { label: 'Descrição da ação', value: 'descricaoAcao' },
        { label: 'Data', value: 'data' },
        { label: 'Usuário', value: 'usuario' },
        { label: 'Link', value: 'acao' }
    ]

    useEffect(() => {
        if (dataFilterAcompanhamento?.competencia) {
            loadDados()
        }
    }, [numberPage, dataFilterAcompanhamento?.competencia])

    useEffect(() => {
        if (typedPersistedFilters && typedPersistedFilters.competencia && !acompanhamentoList.length) {
            loadDados()
        }
    }, [typedPersistedFilters])

    useEffect(() => {
        if (dataFilterAcompanhamento && Object.keys(dataFilterAcompanhamento).length > 0) {
            updatePersistedFilters({
                ...dataFilterAcompanhamento,
                numberPage
            })
        }
    }, [dataFilterAcompanhamento, numberPage])

    return (
        <>
            <S.Wrapper>
                <FitroAcompanhamentos
                    dataFilter={dataFilterAcompanhamento}
                    setDataFilter={setDataFilterAcompanhamento}
                    buscar={(e) => {
                        //setPrestadoresSelecionados([])
                        setNumberPage(0)
                        loadDados(e)
                    }}
                />

                {dataFilterAcompanhamento && Object.keys(dataFilterAcompanhamento).length > 0 && (
                    <div style={{ marginBottom: '16px', marginRight: '20px', textAlign: 'right' }}>
                        <Button variant="outlined" color="secondary" onClick={handleLimparFiltros}>
                            Limpar Filtros
                        </Button>
                    </div>
                )}
            </S.Wrapper>

            {loadingDados ? (
                <Box sx={{ display: 'flex', width: '100%', justifyContent: 'center', alignItems: 'center', height: '70vh' }}>
                    <CircularProgress />
                </Box>
            ) : (
                <>
                    {acompanhamentoList?.length > 0 ? (
                        <>
                            {' '}
                            <div>
                                <S.InformacoesGerais>
                                    <h4>Prestadores</h4>
                                </S.InformacoesGerais>

                                <TablePagination
                                    titles={titles}
                                    values={acompanhamentoList?.map((item) => {
                                        return {
                                            ...item,
                                            nomePrestador: (
                                                <S.NomePrestador>
                                                    <p>{item?.nomePrestador}</p>
                                                    <span>{maskCNPJ(item?.cnpjprestador)}</span>
                                                </S.NomePrestador>
                                            ),
                                            competencia: item?.competencia,
                                            acao:
                                                item?.idDemonstrativo === null ? (
                                                    <div style={{ width: '50px' }}></div>
                                                ) : (
                                                    <S.LinkWrapper
                                                        onClick={() => route.push(`/demonstrativo/demonstrativo-detalhes/${item?.idDemonstrativo}`)}
                                                    >
                                                        <ReactSVG src="/contas/assets/imgs/mai-ic-link.mono.svg" />
                                                    </S.LinkWrapper>
                                                )
                                        }
                                    })}
                                    pagination={pagination}
                                    customGridStyles="1.5fr 1fr 1.5fr 1fr 1fr 0.1fr"
                                />

                                <S.ButtonAction>
                                    <Button color="warning" onClick={() => setOpenModal(true)}>
                                        Exportar Relatório
                                    </Button>
                                </S.ButtonAction>
                            </div>
                            <ExportarAcompanhamentoModal openModal={openModal} setOpenModal={setOpenModal} filtroSelecionado={filtroSelecionado} />
                        </>
                    ) : (
                        <NoItensComponent
                            src="/contas/assets/imgs/notfound.svg"
                            titulo="Ops!... nenhum resultado encontrado"
                            subTitulo="Tente outra pesquisa"
                        />
                    )}
                </>
            )}
        </>
    )
}

export default AcompanhamentoComponent
