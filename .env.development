# PLANSERV

# NEXT_PUBLIC_APP_NAME=contas
# NEXT_PUBLIC_ENVIRONMENT=staging
# NEXT_PUBLIC_CLIENT_ASSETS=planserv
# NEXT_PUBLIC_DEV=true
# NEXT_PUBLIC_WORKSPACE=https://planserv.staging.maida.health/workspace/
# NEXT_PUBLIC_SSO=https://planserv.staging.maida.health/sso
# NEXT_PUBLIC_MANAGEMENT=https://pv-gestao-usuarios-api-staging.cloud.maida.health
# NEXT_PUBLIC_API_CREDENCIAMENTO=https://pv-credenciamento-api-staging.cloud.maida.health/
# NEXT_PUBLIC_API_CONTAS_PAGAR_WSS=ws://10.10.0.13:8080/websocket
# NEXT_PUBLIC_EFD_ENV=false
# NEXT_PUBLIC_API_CONTAS_PAGAR=https://pv-contas-a-pagar-api-staging.cloud.maida.health/
# NEXT_PUBLIC_API_CONTAS_PAGAR_WSS=wss://pv-contas-a-pagar-api-staging.cloud.maida.health/websocket
# # NEXT_PUBLIC_EFD_ENV=true
# NEXT_PUBLIC_CLIENT=planserv

# INAS GDF

NEXT_PUBLIC_API_COBRANCA=https://cobranca-api-staging.cloud.maida.health
NEXT_PUBLIC_API_CONSUMO=https://consumo-api-staging.cloud.maida.health
NEXT_PUBLIC_API_CONTAS_PAGAR=https://contas-a-pagar-api-staging.cloud.maida.health/
# NEXT_PUBLIC_API_CONTAS_PAGAR=https://66cb-2804-28c8-c32f-7000-e13a-c007-6d97-26c3.ngrok-free.app/
NEXT_PUBLIC_API_CONTAS_PAGAR_WSS=wss://contas-a-pagar-api-staging.cloud.maida.health/websocket
NEXT_PUBLIC_API_CONTRATOS=https://contrato-api-staging.cloud.maida.health
NEXT_PUBLIC_API_FINANCEIRO=https://financeiro-api-staging.cloud.maida.health
NEXT_PUBLIC_APP_NAME=contas
NEXT_PUBLIC_CLIENT=gdf
NEXT_PUBLIC_CLIENT_ASSETS=gdf
NEXT_PUBLIC_EFD_ENV=false
NEXT_PUBLIC_ENVIRONMENT=staging
NEXT_PUBLIC_FATURAMENTO=https://staging.maida.health/faturamento
NEXT_PUBLIC_MANAGEMENT=https://gestao-usuarios-api-staging.cloud.maida.health
NEXT_PUBLIC_SSO=https://staging.maida.health/sso
NEXT_PUBLIC_WORKSPACE=https://staging.maida.health/workspace/

# GDF LIVE

# NEXT_PUBLIC_APP_NAME=contas
# NEXT_PUBLIC_ENVIRONMENT=live
# NEXT_PUBLIC_CLIENT_ASSETS=gdf
# NEXT_PUBLIC_DEV=true
# NEXT_PUBLIC_WORKSPACE=https://staging.maida.health/workspace/
# NEXT_PUBLIC_SSO=https://staging.maida.health/sso
# NEXT_PUBLIC_MANAGEMENT=https://df-gestao-usuarios-api-live.gdf.live.maida.health
# NEXT_PUBLIC_API_CREDENCIAMENTO=https://df-credenciamento-api-live.gdf.live.maida.health/
# NEXT_PUBLIC_EFD_ENV=false
# NEXT_PUBLIC_API_CONTAS_PAGAR=https://df-contas-a-pagar-api-live.gdf.live.maida.health/
# NEXT_PUBLIC_API_CONTAS_PAGAR_WSS=wss://df-contas-a-pagar-api-live.gdf.live.maida.health/websocket
# NEXT_PUBLIC_CLIENT=gdf
