import { Button } from '@mui/material'
import Badge from 'components/atoms/Badge'
import NoItensComponent from 'components/atoms/NoItensComponent'
import TablePagination from 'components/molecules/TablePagination'
import UserContext from 'context/UserContext/UserContext'
import { useFilterPersistence } from 'hooks/storage/useFilterPersistence'
import { useRouter } from 'next/router'
import { useContext, useEffect, useRef, useState } from 'react'
import { ReactSVG } from 'react-svg'
import { ProfilesEnum } from 'src/enum/profiles.enum'
import { DescontosFinanceiros, IGetDescontosFinanceiros } from 'src/services/ContasPagar/descontos-financeiros'
import { IDescontoFinanceiroResumoListagemDTO } from 'src/services/ContasPagar/descontos-financeiros/descontos-financeiros'
import { IPagination } from 'src/types/pagination'
import { maskMon } from 'utils/masks'
import { maskCNPJ } from 'utils/masks/formatCNPJ'
import { parserPagination } from 'utils/parsePagination'
import { BadgesBackgroundEnum, BadgesColorEnum, BadgesTextEnum, tipoDescontoEnum } from './enuns'
import FitroDescontoFinanceiro from './filtros/FIltroDescontoFinanceiro'
import * as S from './styles'

// Interface para filtros persistidos incluindo paginação
interface IPersistedFilters extends Omit<IGetDescontosFinanceiros, 'competencia'> {
    page: number
    competencia?: string | null
}

const DescontoFinanceiroTemplate = () => {
    const route = useRouter()

    // Hook para persistência dos filtros e paginação
    const {
        filters: persistedFilters,
        updateFilters: updatePersistedFilters,
        clearFilters: clearPersistedFilters
    } = useFilterPersistence({
        route: '/desconto-financeiro',
        filterType: 'listagem-descontos',
        defaultValues: {
            competencia: null,
            nomeCNPJCodigo: '',
            situacao: null,
            page: 0
        }
    })

    const typedPersistedFilters = persistedFilters as IPersistedFilters | null

    // Função para normalizar a data de competência (sempre dia 1, sem timezone)
    const normalizeCompetencia = (date: Date | string | null): string | null => {
        if (!date) return null

        const d = new Date(date)
        if (isNaN(d.getTime())) return null

        // Criar data no primeiro dia do mês, sem timezone
        const year = d.getFullYear()
        const month = d.getMonth()
        return `${year}-${String(month + 1).padStart(2, '0')}-01`
    }

    // Função para converter data normalizada para Date (para o DatePicker)
    const denormalizeCompetencia = (dateString: string | null): Date | null => {
        if (!dateString) return null

        const [year, month] = dateString.split('-').map(Number)
        if (year && month !== undefined) {
            // month - 1 porque Date() usa 0-11 para meses
            return new Date(year, month - 1, 1)
        }
        return null
    }

    const [dataFilter, setDataFilter] = useState<IGetDescontosFinanceiros>(() => {
        if (typedPersistedFilters && Object.keys(typedPersistedFilters).length > 0) {
            return typedPersistedFilters
        }
        return {} as IGetDescontosFinanceiros
    })

    // Estado local para competência (Date para o DatePicker)
    const [competenciaLocal, setCompetenciaLocal] = useState<Date | null>(() => {
        if (typedPersistedFilters?.competencia) {
            const [year, month] = typedPersistedFilters.competencia.split('-').map(Number)
            if (year && month !== undefined) {
                return new Date(year, month - 1, 1)
            }
        }
        return null
    })

    const [numberPage, setNumberPage] = useState<number>(typedPersistedFilters?.page || 0)
    const [discountList, setDiscountList] = useState<IDescontoFinanceiroResumoListagemDTO[]>([] as IDescontoFinanceiroResumoListagemDTO[])
    const [pagination, setPagination] = useState<IPagination>()
    const { conditionRender, userProfiles } = useContext(UserContext)

    // Referência para controlar se é a primeira execução
    const isInitialMount = useRef(true)

    const updatePage = (newPage: number) => {
        setNumberPage(newPage)
        if (typedPersistedFilters && Object.keys(typedPersistedFilters).length > 0) {
            updatePersistedFilters({
                ...typedPersistedFilters,
                page: newPage
            })
        }
    }

    const titles: { label: string; value: keyof IDescontoFinanceiroResumoListagemDTO | 'acao' }[] = [
        { label: 'Nome', value: 'nomePrestador' },
        { label: 'Descrição', value: 'descricao' },
        { label: 'CNPJ', value: 'cnpjPrestador' },
        { label: 'Tipo de Desconto', value: 'tipo' },
        { label: 'Nº Parcelas', value: 'numeroParcelas' },
        { label: 'Valor', value: 'valorMeta' },
        { label: 'Situação', value: 'status' },
        { label: '', value: 'acao' }
    ]

    function getData({ filters, page = 0 }: { filters: IGetDescontosFinanceiros; page?: number }) {
        DescontosFinanceiros.get({ ...filters, size: 5, page }).then(({ data }) => {
            setDiscountList(data.content)
            setPagination(parserPagination({ page: data, setNumberPage: updatePage }))
        })
    }

    useEffect(() => {
        if (typedPersistedFilters && Object.keys(typedPersistedFilters).length > 0 && !discountList.length) {
            const pageToLoad = typedPersistedFilters.page || 0

            setDataFilter(typedPersistedFilters)

            if (typedPersistedFilters.competencia) {
                const [year, month] = typedPersistedFilters.competencia.split('-').map(Number)
                if (year && month !== undefined) {
                    setCompetenciaLocal(new Date(year, month - 1, 1))
                }
            }

            getData({ filters: typedPersistedFilters, page: pageToLoad })
        } else if (!typedPersistedFilters || Object.keys(typedPersistedFilters).length === 0) {
            DescontosFinanceiros.get({ size: 5, page: 0 }).then(({ data }) => {
                setDiscountList(data.content)
                setPagination(parserPagination({ page: data, setNumberPage: updatePage }))
            })
        }
    }, [])

    useEffect(() => {
        if (isInitialMount.current) {
            isInitialMount.current = false
            return
        }

        if (dataFilter && Object.keys(dataFilter).length > 0) {
            const currentFilters = {
                ...dataFilter,
                competencia: competenciaLocal
                    ? `${competenciaLocal.getFullYear()}-${String(competenciaLocal.getMonth() + 1).padStart(2, '0')}-01`
                    : null
            }
            getData({ filters: currentFilters, page: numberPage })
        } else {
            DescontosFinanceiros.get({ size: 5, page: numberPage }).then(({ data }) => {
                setDiscountList(data.content)
                setPagination(parserPagination({ page: data, setNumberPage: updatePage }))
            })
        }
    }, [numberPage, dataFilter, competenciaLocal])

    useEffect(() => {
        if (dataFilter && Object.keys(dataFilter).length > 0) {
            const competenciaNormalizada = competenciaLocal
                ? `${competenciaLocal.getFullYear()}-${String(competenciaLocal.getMonth() + 1).padStart(2, '0')}-01`
                : null

            updatePersistedFilters({
                ...dataFilter,
                competencia: competenciaNormalizada,
                page: numberPage
            })
        }
    }, [dataFilter, competenciaLocal, numberPage])

    useEffect(() => {
        if (typedPersistedFilters && Object.keys(typedPersistedFilters).length > 0 && numberPage !== typedPersistedFilters.page) {
            updatePersistedFilters({
                ...typedPersistedFilters,
                page: numberPage
            })
        }
    }, [numberPage])

    return (
        <S.BodyContainer>
            <S.Wrapper>
                <FitroDescontoFinanceiro
                    dataFilter={dataFilter}
                    setDataFilter={setDataFilter}
                    competenciaLocal={competenciaLocal}
                    setCompetenciaLocal={setCompetenciaLocal}
                    buscar={(filters) => {
                        setDataFilter(filters)
                        setNumberPage(0)
                        updatePersistedFilters({
                            ...filters,
                            page: 0
                        })
                        getData({ filters, page: 0 })
                    }}
                />
                {dataFilter && Object.keys(dataFilter).length > 0 && (
                    <div style={{ textAlign: 'right', marginTop: '25px' }}>
                        <Button
                            variant="outlined"
                            color="secondary"
                            onClick={() => {
                                setDataFilter({} as IGetDescontosFinanceiros)
                                setNumberPage(0)
                                setCompetenciaLocal(null)
                                clearPersistedFilters()
                                DescontosFinanceiros.get({ size: 5, page: 0 }).then(({ data }) => {
                                    setDiscountList(data.content)
                                    setPagination(parserPagination({ page: data, setNumberPage: updatePage }))
                                })
                            }}
                        >
                            Limpar Filtros
                        </Button>
                    </div>
                )}
            </S.Wrapper>
            {discountList?.length > 0 ? (
                <>
                    <div>
                        <S.ListagemPrestadores>
                            <p>Descontos</p>

                            {conditionRender(
                                (!userProfiles?.profiles?.includes(ProfilesEnum?.CONTAS_A_PAGAR_LEITOR) &&
                                    userProfiles?.profiles?.includes(ProfilesEnum?.CONTAS_A_PAGAR_EDITOR)) ||
                                    userProfiles?.profiles?.includes(ProfilesEnum?.OWNER),
                                <Button
                                    color="warning"
                                    startIcon={<ReactSVG src="/contas/assets/icons/plus.svg" />}
                                    onClick={() => route.push(`/desconto-financeiro/criar-desconto`)}
                                >
                                    Registrar desconto
                                </Button>
                            )}
                        </S.ListagemPrestadores>
                        <TablePagination
                            titles={titles}
                            values={discountList?.map((item) => {
                                return {
                                    ...item,
                                    nomePrestador: <S.ListagemDestaque>{item?.nomePrestador}</S.ListagemDestaque>,
                                    numeroParcelas: item?.numeroParcelas,
                                    cnpjPrestador: maskCNPJ(item?.cnpjPrestador),
                                    tipo: tipoDescontoEnum[item?.tipo],
                                    valorMeta: maskMon(item?.valorMeta?.toFixed(2)),
                                    status: (
                                        <div style={{ width: 'fit-content' }}>
                                            <Badge
                                                color={BadgesColorEnum[item?.status]}
                                                background={BadgesBackgroundEnum[item?.status]}
                                                text={BadgesTextEnum[item?.status]}
                                            />
                                        </div>
                                    ),
                                    acao: (
                                        <S.ActionWrapper>
                                            <S.ActionButton
                                                onClick={() => route.push(`/desconto-financeiro/visualizar-desconto/${item?.descontoUuid}`)}
                                            >
                                                <ReactSVG src="/contas/assets/imgs/mai-ic-visible-true.mono.svg" />
                                            </S.ActionButton>

                                            {conditionRender(
                                                (!userProfiles?.profiles?.includes(ProfilesEnum?.CONTAS_A_PAGAR_LEITOR) &&
                                                    userProfiles?.profiles?.includes(ProfilesEnum?.CONTAS_A_PAGAR_EDITOR)) ||
                                                    userProfiles?.profiles?.includes(ProfilesEnum?.OWNER),

                                                <S.ActionButton
                                                    onClick={() => route.push(`/desconto-financeiro/editar-desconto/${item?.descontoUuid}`)}
                                                >
                                                    <ReactSVG src="/contas/assets/imgs/mai-ic-edit.mono.svg" />
                                                </S.ActionButton>
                                            )}
                                        </S.ActionWrapper>
                                    )
                                }
                            })}
                            pagination={pagination}
                            customGridStyles="2fr 1.8fr 1.5fr 1fr 1fr 1fr 0.9fr 0.6fr"
                        />
                    </div>
                </>
            ) : (
                <>
                    <S.ListagemPrestadores>
                        <p></p>
                        <Button
                            color="warning"
                            startIcon={<ReactSVG src="/contas/assets/icons/plus.svg" />}
                            onClick={() => route.push(`/desconto-financeiro/criar-desconto`)}
                        >
                            Registrar desconto
                        </Button>
                    </S.ListagemPrestadores>
                    <NoItensComponent
                        src="/contas/assets/imgs/notfound.svg"
                        titulo="Ops!... nenhum resultado encontrado"
                        subTitulo="Tente outra pesquisa"
                    />
                    {/* <PaginationNew
                        totalPage={pagination.totalPaginas}
                        totalRegister={pagination.totalRegistros}
                        actualPage={pagination.paginaAtual}
                        setNumberPage={setNumberPage}
                    /> */}
                </>
            )}
        </S.BodyContainer>
    )
}

export default DescontoFinanceiroTemplate

