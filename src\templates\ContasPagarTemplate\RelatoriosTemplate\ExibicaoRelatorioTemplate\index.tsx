/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-unused-vars */
import { Box, Button, CircularProgress } from '@mui/material'
import { useEffect, useState } from 'react'
import { IPagination } from 'src/types/pagination'
import { downloadFileV2 } from 'utils/functions'
import { maskMon } from 'utils/masks'
import * as S from './styles'
// import { SituacaoDemonstrativoNFEnum } from '../enuns'
import NoItensComponent from 'components/atoms/NoItensComponent'
import TablePagination from 'components/molecules/TablePagination'
import { useToast } from 'hooks/toast'
import { useRouter } from 'next/router'
import { ReactSVG } from 'react-svg'
import { ExtratoProducao } from 'services/ContasPagar/extrato-producao-controller'
import { IGetPrestadoresProps } from 'services/ContasPagar/extrato-producao-controller/types'
import { EnumTipoPrestador } from 'src/services/ContasPagar/prestador-controller/enuns'
import { Relatorios } from 'src/services/ContasPagar/relatorio-controller'
import { DateUtils } from 'utils/date-utils'
import { PaginationHelper } from 'utils/pagination-helper'
import ContadorRodape from '../ContadorRodapeTemplate'
import { TipoRelatorio } from '../enuns'
import { TipoRelatorioEnum } from '../FitroHistoricoIntegracao/enums'
import {
    IAcompanhamentoPagamentoProps,
    IDataFilterRelatorio,
    ILiquidacoesProps,
    IProducaoAprovadaProps,
    IRetencaoISSProps
} from '../FitroHistoricoIntegracao/filtro-types'
import InformacoesGeraisRelatorio from '../InformacoesGeraisTemplate'
import ExportarRelatorioModal from '../modais/ModalExportarRelatorio'

// const titlesProducaoAprovada: { label: string; value: any | 'acao' }[] = [
//     { label: 'Código Prest.', value: 'codigoPrestador' },
//     { label: 'Prestador', value: 'nomePrestador' },
//     { label: 'Município', value: 'EMP' },
//     { label: 'Tipo Credor', value: 'NF' },
//     // { label: 'Aprovação', value: 'dataDemonstrativo' },
//     { label: 'Banco', value: 'valorBruto' },
//     { label: 'Agência', value: 'valorLiquido' },
//     { label: 'Conta Corrente', value: 'IssRetido' },
//     { label: 'Data Aprovação', value: 'IrRetido' },
//     { label: 'Valor aprovado', value: 'outros' },
//     { label: '', value: 'acao' }
// ]

const titlesProducaoAprovada: { label: string; value: any | 'acao' }[] = [
    { label: 'Código Prest.', value: 'codigoPrestador' },
    { label: 'Prestador', value: 'nomePrestador' },
    { label: 'Município', value: 'municipio' },
    { label: 'Tipo Credor', value: 'tipoCredor' },
    { label: 'Banco', value: 'banco' },
    { label: 'Agência', value: 'agencia' },
    { label: 'Conta Corrente', value: 'contaCorrente' },
    { label: 'Data Aprovação', value: 'dataAprovacao' },
    { label: 'Valor aprovado', value: 'valorAprovadoFormatado' }
]

const titlesRetencaoISS: { label: string; value: any | 'acao' }[] = [
    { label: 'Prestador', value: 'nomePrestador' },
    { label: 'Nº EMP', value: 'numeroEmp' },
    { label: 'Nº NF', value: 'numeroNotaFiscal' },
    { label: 'Valor bruto', value: 'valorBrutoFormatado' },
    { label: 'Data da LIQ', value: 'dataArquivoLiq' },
    { label: 'Pagamento', value: 'dataPagamento' },
    { label: 'Valor líquido', value: 'valorLiquidoFormatado' },
    { label: 'ISS retido', value: 'valorISSRetidoFormatado' },
    { label: 'Alíquiota ISS', value: 'aliquotaISS' },
    { label: 'IR retido', value: 'valorIRRetidoFormatado' },
    { label: 'Outros desc.', value: 'valorOutrosDescontosFormatado' }
]

const titlesLiquidacoes: { label: string; value: any | 'acao' }[] = [
    { label: 'Nome do prestador', value: 'nomePrestador' },
    { label: 'Nº EMP', value: 'numeroEmp' },
    { label: 'Nº NF', value: 'numeroNotaFiscal' },
    { label: 'Valor bruto', value: 'valorBrutoFormatado' },
    { label: 'Data da LIQ', value: 'dataArquivoLiq' },
    { label: 'Pagamento', value: 'dataPagamento' },
    { label: 'Valor líquido', value: 'valorLiquidoFormatado' },
    { label: 'ISS retido', value: 'valorISSRetidoFormatado' },
    { label: 'IR retido', value: 'valorIRRetidoFormatado' },
    { label: 'Outros desc.', value: 'valorOutrosDescontosFormatado' }
]

const titlesAcompanhamentoPagamento: { label: string; value: any | 'acao' }[] = [
    { label: 'Nome do prestador', value: 'nomePrestador' },
    { label: 'Situação pagamento', value: 'situacaoPagamento' },
    { label: 'Nº EMP', value: 'numeroEmp' },
    { label: 'Nº NF', value: 'numeroNotaFiscal' },
    { label: 'Data Demonstrativo', value: 'dataDemonstrativo' },
    { label: 'Data de pag.', value: 'dataPagamento' },
    { label: 'Valor bruto', value: 'valorBrutoFormatado' },
    { label: 'Valor líquido', value: 'valorLiquidoFormatado' },
    { label: 'ISS retido', value: 'valorISSRetidoFormatado' },
    { label: 'IR retido', value: 'valorIRRetidoFormatado' },
    { label: 'Outros desc.', value: 'valorOutrosDescontosFormatado' },
    { label: '', value: 'acao' }
]

const titlesDemonstrativoPagamento: { label: string; value: any | 'acao' }[] = [
    { label: 'Nome do prestador', value: 'nomePrestador' },
    { label: 'Competencia', value: 'competencia' },
    { label: 'Valor bruto', value: 'valorBrutoFormatado' },
    { label: '', value: 'acao' }
]

const ExibicaoRelatorioTemplate = () => {
    const router = useRouter()
    const { addToast } = useToast()
    const data = router.query.data as string

    const [dataFilterRelatorio, setDataFilterRelatorio] = useState<IDataFilterRelatorio>()
    const [loadingDados, setLoadingDados] = useState(true)
    const [openModal, setOpenModal] = useState(false)

    const [relatorioProps, setRelatorioProps] = useState<any>()

    const [titles, setTitles] = useState<any>()
    const [gridStyle, setGridStyle] = useState<any>('1.5fr 1.5fr 0.5fr 0.7fr 0.7fr 0.7fr 0.7fr 0.7fr 0.7fr 0.7fr 0.7fr')
    const [prestadoresList, setPrestadoresList] = useState<any>()
    const [pagination, setPagination] = useState<IPagination>()
    const [numberPage, setNumberPage] = useState<number>(0)
    const [disableBtn, setDisableBtn] = useState<boolean>(false)

    useEffect(() => {
        if (data) setDataFilterRelatorio(JSON.parse(decodeURIComponent(data)) as IDataFilterRelatorio)
    }, [router.isReady])

    useEffect(() => {
        if (dataFilterRelatorio?.tipoRelatorio === TipoRelatorioEnum.PRODUCAO_APROVADA) {
            Relatorios.getProducaoAprovada(loadDadosProducaoAprovada())
                .then(({ data }) => {
                    setTitles(titlesProducaoAprovada)
                    setGridStyle('.8fr 1.5fr 0.5fr 0.7fr 0.7fr 0.7fr 0.7fr 0.7fr 0.7fr 0.1fr')
                    setPrestadoresList(data)
                    setPagination(PaginationHelper.parserPagination(data.prestadores, setNumberPage))
                })
                .catch((err) =>
                    addToast({
                        title: err?.response?.data?.message ? err?.response?.data?.message : 'Ocorreu um erro: Desconhecido',
                        type: 'error',
                        duration: 5000
                    })
                )
                .finally(() => {
                    setLoadingDados(false)
                })
        }
        if (dataFilterRelatorio?.tipoRelatorio === TipoRelatorioEnum.LIQUIDACOES) {
            Relatorios.getLiquidacoes(loadDadosLiquidacoes())
                .then(({ data }) => {
                    setTitles(titlesLiquidacoes)
                    setGridStyle('1.5fr 1.5fr 0.5fr 0.7fr 0.7fr 0.7fr 0.7fr 0.7fr 0.7fr 0.7fr')
                    setPrestadoresList(data)
                    setPagination(PaginationHelper.parserPagination(data.prestadores, setNumberPage))
                })
                .catch((err) =>
                    addToast({
                        title: err?.response?.data?.message ? err?.response?.data?.message : 'Ocorreu um erro: Desconhecido',
                        type: 'error',
                        duration: 5000
                    })
                )
                .finally(() => {
                    setLoadingDados(false)
                })
        }
        if (dataFilterRelatorio?.tipoRelatorio === TipoRelatorioEnum.ACOMPANHAMENTO_PAGAMENTO) {
            Relatorios.getAcompanhamentoPagamento(loadDadosAcompanhamentoPagamento())
                .then(({ data }) => {
                    setTitles(titlesAcompanhamentoPagamento)
                    setGridStyle('1.5fr 1fr 1fr 0.5fr 0.7fr 0.7fr 0.7fr 0.7fr 0.7fr 0.7fr 0.1fr')
                    setPrestadoresList(data)
                    setPagination(PaginationHelper.parserPagination(data.prestadores, setNumberPage))
                })
                .catch((err) =>
                    addToast({
                        title: err?.response?.data?.message ? err?.response?.data?.message : 'Ocorreu um erro: Desconhecido',
                        type: 'error',
                        duration: 5000
                    })
                )
                .finally(() => {
                    setLoadingDados(false)
                })
        }
        if (dataFilterRelatorio?.tipoRelatorio === TipoRelatorioEnum.EXTRATO_PRODUCAO) {
            ExtratoProducao.getPrestadores(loadDadosDemonstrativoPagamento())
                .then(({ data }) => {
                    setTitles(titlesDemonstrativoPagamento)
                    setGridStyle('1.5fr 1fr 1fr 0.1fr')
                    setPrestadoresList(data)
                    setPagination(PaginationHelper.parserPagination(data, setNumberPage))
                })
                .catch((err) =>
                    addToast({
                        title: err?.response?.data?.message ? err?.response?.data?.message : 'Ocorreu um erro: Desconhecido',
                        type: 'error',
                        duration: 5000
                    })
                )
                .finally(() => {
                    setLoadingDados(false)
                })
        }
        if (dataFilterRelatorio?.tipoRelatorio === TipoRelatorioEnum.RETENCOES_ISS) {
            Relatorios.getRetencaoISS(loadDadosRetencaoISS())
                .then(({ data }) => {
                    setTitles(titlesRetencaoISS)
                    setGridStyle('1.5fr 1.3fr 0.5fr 0.8fr 0.7fr 0.7fr 0.8fr 0.7fr 0.7fr 0.7fr 0.7fr')
                    setPrestadoresList(data)
                    setPagination(PaginationHelper.parserPagination(data.prestadores, setNumberPage))
                })
                .catch((err) =>
                    addToast({
                        title: err?.response?.data?.message ? err?.response?.data?.message : 'Ocorreu um erro: Desconhecido',
                        type: 'error',
                        duration: 5000
                    })
                )
                .finally(() => {
                    setLoadingDados(false)
                })
        }
    }, [dataFilterRelatorio, numberPage])

    const loadDadosProducaoAprovada = (): IProducaoAprovadaProps => {
        const data = {
            competencia: dataFilterRelatorio?.competencia,
            dataInicialAprovacao: dataFilterRelatorio?.dataAprovacaoInicial,
            dataFinalAprovacao: dataFilterRelatorio?.dataAprovacaoFinal,
            prestadoresIds: dataFilterRelatorio?.prestadoresList?.map((item) => item.value) || [],
            tipoPrestador: EnumTipoPrestador[dataFilterRelatorio?.tipoPrestador?.value],
            page: numberPage
        }

        setRelatorioProps(data)
        return data
    }

    const loadDadosRetencaoISS = (): IRetencaoISSProps => {
        const data: IRetencaoISSProps = {
            competencia: dataFilterRelatorio?.competencia,
            prestadoresIds: dataFilterRelatorio?.prestadoresList?.map((item) => item.value) || [],
            dataInicialPagamento: dataFilterRelatorio?.dataInicialPagamento,
            dataFinalPagamento: dataFilterRelatorio?.dataFinalPagamento,
            codigoIbgeMunicipio: dataFilterRelatorio?.codigoIbgeMunicipio,
            separarMunicipiosEmPaginas: dataFilterRelatorio?.separarMunicipios ?? false,
            page: numberPage
        }

        setRelatorioProps(data)
        return data
    }

    const loadDadosLiquidacoes = (): ILiquidacoesProps => {
        const data: ILiquidacoesProps = {
            competencia: dataFilterRelatorio?.competencia,
            prestadoresIds: dataFilterRelatorio?.prestadoresList?.map((item) => item.value) || [],
            dataInicialPagamento: dataFilterRelatorio?.dataInicialPagamento,
            dataFinalPagamento: dataFilterRelatorio?.dataFinalPagamento,
            dataInicialGeracaoLiq: dataFilterRelatorio?.dataInicialGeracaoLiq,
            dataFinalGeracaoLiq: dataFilterRelatorio?.dataFinalGeracaoLiq,
            page: numberPage
        }

        setRelatorioProps(data)
        return data
    }

    const loadDadosAcompanhamentoPagamento = (): IAcompanhamentoPagamentoProps => {
        const data = {
            competencia: dataFilterRelatorio?.competencia,
            prestadoresIds: dataFilterRelatorio?.prestadoresList?.map((item) => item.value) || [],
            dataInicialGeracaoDemonstrativo: dataFilterRelatorio?.dataInicialDemonstrativo,
            dataFinalGeracaoDemonstrativo: dataFilterRelatorio?.dataFinalDemonstrativo,
            situacaoPagamento: dataFilterRelatorio?.situacaoPagamento,
            page: numberPage
        }

        setRelatorioProps(data)
        return data
    }

    const loadDadosDemonstrativoPagamento = (): IGetPrestadoresProps => {
        const data = {
            competencia: dataFilterRelatorio?.competencia,
            prestadoresIds: dataFilterRelatorio?.prestadoresList?.map((item) => item.value) || [],
            tipoPrestador: EnumTipoPrestador[dataFilterRelatorio?.tipoPrestador?.value],
            page: numberPage
        }

        setRelatorioProps(data)
        return data
    }

    function downloadRelatorio(prestadorId: string, competencia: string) {
        ExtratoProducao.getPdf({
            prestadorId,
            competencia: DateUtils.toIsoString({ localeDateString: '01/' + competencia })
        })
            .then((response) => {
                downloadFileV2(response.data, 'ExtratoProducao.xlsx')
            })
            .catch((err) =>
                addToast({
                    title: err?.response?.data?.message ? err?.response?.data?.message : 'Ocorreu um erro: Desconhecido',
                    type: 'error',
                    duration: 5000
                })
            )
    }

    return (
        <>
            {loadingDados ? (
                <Box sx={{ display: 'flex', width: '100%', justifyContent: 'center', alignItems: 'center', height: '70vh' }}>
                    <CircularProgress />
                </Box>
            ) : (
                <>
                    <S.BoddyContainer>
                        <S.Title>{TipoRelatorio[dataFilterRelatorio?.tipoRelatorio]}</S.Title>

                        <InformacoesGeraisRelatorio dataFilterRelatorio={dataFilterRelatorio} />
                        <div>
                            <S.InformacoesGerais>
                                <h4>Prestadores</h4>
                            </S.InformacoesGerais>
                            {(dataFilterRelatorio?.tipoRelatorio === TipoRelatorioEnum.EXTRATO_PRODUCAO
                                ? prestadoresList?.content
                                : prestadoresList?.prestadores?.content
                            )?.length > 0 ? (
                                <>
                                    <TablePagination
                                        onClickElement="id"
                                        titles={titles ?? []}
                                        values={(dataFilterRelatorio?.tipoRelatorio === TipoRelatorioEnum.EXTRATO_PRODUCAO
                                            ? prestadoresList
                                            : prestadoresList?.prestadores
                                        )?.content?.map((item) => {
                                            return {
                                                ...item,
                                                nomePrestador: (
                                                    <S.PrestadorInfo>
                                                        <p>{item?.nomePrestador}</p>
                                                        <span>{item?.cpfCnpj}</span>
                                                    </S.PrestadorInfo>
                                                ),
                                                // valorBruto: maskMon(item?.valorBruto?.toFixed(2)),
                                                // valorLiquido: maskMon(item?.valorLiquido?.toFixed(2)),
                                                // IssRetido: maskMon(item?.IssRetido?.toFixed(2)),
                                                // IrRetido: maskMon(item?.IrRetido?.toFixed(2)),
                                                aliquotaISS: maskMon(Number(item?.aliquotaISS).toFixed(2)),
                                                outros: maskMon(item?.outros?.toFixed(2)),
                                                acao: dataFilterRelatorio?.tipoRelatorio === TipoRelatorioEnum.EXTRATO_PRODUCAO && (
                                                    <S.IconDownloadWrapper
                                                        onClick={() => {
                                                            downloadRelatorio(item?.prestadorId, item?.competencia)
                                                        }}
                                                    >
                                                        <ReactSVG src="/contas/assets/icons/mai-ic-download.svg" />
                                                    </S.IconDownloadWrapper>
                                                )
                                            }
                                        })}
                                        pagination={pagination}
                                        customGridStyles={gridStyle ?? '1.5fr 1.5fr 0.5fr 0.7fr 0.7fr 0.7fr 0.7fr 0.7fr 0.7fr 0.7fr 0.7fr'}
                                    />
                                    {dataFilterRelatorio?.tipoRelatorio !== TipoRelatorioEnum.EXTRATO_PRODUCAO && (
                                        <ContadorRodape data={prestadoresList} tipoRelatorio={dataFilterRelatorio?.tipoRelatorio} />
                                    )}

                                    {dataFilterRelatorio?.tipoRelatorio !== TipoRelatorioEnum.EXTRATO_PRODUCAO && (
                                        <S.ButtonAction>
                                            <Button
                                                disabled={disableBtn}
                                                onClick={() => {
                                                    setOpenModal(true)
                                                }}
                                                color="warning"
                                            >
                                                {disableBtn ? 'Gerando...' : 'Exportar relatório'}
                                            </Button>
                                        </S.ButtonAction>
                                    )}
                                </>
                            ) : (
                                <NoItensComponent
                                    src="/contas/assets/imgs/notfound.svg"
                                    titulo="Ops!... nenhum resultado encontrado"
                                    subTitulo="Tente outra pesquisa"
                                    style={{ background: '#ffffff' }}
                                />
                            )}
                        </div>
                        <ExportarRelatorioModal
                            openModal={openModal}
                            setOpenModal={setOpenModal}
                            tipoRelatorio={dataFilterRelatorio?.tipoRelatorio}
                            dataFilter={relatorioProps}
                            setDisableBtn={setDisableBtn}
                        />
                    </S.BoddyContainer>
                </>
            )}
        </>
    )
}

export default ExibicaoRelatorioTemplate
