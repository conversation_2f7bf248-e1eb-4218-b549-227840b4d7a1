import styled, { css } from 'styled-components'

export const BodyContainer = styled.div`
    display: flex;
    align-items: center;
    padding: 24px;
    gap: 24px;
    background: #ffffff;
    border-radius: 8px;
`

export const FilterRow0 = styled.div`
    display: grid;
    width: 100%;
    gap: 24px;
    grid-template-columns: 1fr 1fr 1.7fr;
`
export const SearchWrapper = styled.div`
    display: flex;
    gap: 24px;
`
export const IconSearchWrapper = styled.div`
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    padding: 16px;
    width: 56px;
    height: 56px;
    background: #ffcc00;
    border-radius: 100px;
    cursor: pointer;
`

export const WrapperSelected = styled.div`
    /* margin-top: 40px; */
    width: 100%;
    height: 52px;
    background: #2b45d4;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 8px 16px;
    border-radius: 4px;

    > div {
        display: flex;
        align-items: center;
        color: white;
    }

    > img {
        cursor: pointer;
    }

    > div > div {
        display: flex;
        flex-direction: column;

        > :first-child {
            font-style: normal;
            font-weight: 700;
            font-size: 14px;
            line-height: 20px;
            text-transform: uppercase;
        }

        > :last-child {
            font-family: 'Open Sans';
            font-style: normal;
            font-weight: 400;
            font-size: 12px;
            line-height: 16px;
        }
    }
`

