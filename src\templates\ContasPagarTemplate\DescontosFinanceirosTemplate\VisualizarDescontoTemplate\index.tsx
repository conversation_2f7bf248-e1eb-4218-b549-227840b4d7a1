import { Button, Grid } from '@mui/material'
import Badge from 'components/atoms/Badge'
import NoItensComponent from 'components/atoms/NoItensComponent'
import SectionContainer from 'components/atoms/SectionContainer'
import TablePagination from 'components/molecules/TablePagination'
import AttachmentViewModal from 'components/organisms/AttachmentViewModal'
import { useToast } from 'hooks/toast'
import { useRouter } from 'next/router'
import { useEffect, useState } from 'react'
import { ReactSVG } from 'react-svg'
import { ConsultaDocumentosController } from 'services/ContasPagar/consulta-documentos-controller'
import { DescontosFinanceiros } from 'src/services/ContasPagar/descontos-financeiros'
import { IDescontoFinanceiroDTO, IParcelaDescontoFinanceiroDTO } from 'src/services/ContasPagar/descontos-financeiros/descontos-financeiros'
import { IPagination } from 'src/types/pagination'
import { downloadFileV2, fileNameFromBlob, parseDataToMMYYYY } from 'utils/functions'
import { maskMon } from 'utils/masks'
import { maskCNPJ } from 'utils/masks/formatCNPJ'
import { moneyParser } from 'utils/money'
import { BadgesBackgroundEnum, BadgesColorEnum, BadgesTextEnum, statusDescontoEnum, tipoDescontoEnum } from './enuns'
import * as S from './styles'
import { ArquivosListMock } from './mock'

type VisualizarDescontoProps = {
    id?: string
}

const VisualizarDescontoTemplate = ({ id }: VisualizarDescontoProps) => {
    const route = useRouter()
    const { addToast } = useToast()
    // const [parcelaList, setParcelaList] = useState<any[]>(ParcelaListMock)
    const [dataDiscount, setDataDiscount] = useState<IDescontoFinanceiroDTO>({} as IDescontoFinanceiroDTO)
    const [pagination, setPagination] = useState<IPagination>()
    // const [numberPage, setNumberPage] = useState<number>(0)
    const [fileUrl, setFileUrl] = useState<string>()
    const [pageId, setPageId] = useState<any>()

    const [fileModal, setFileModal] = useState(false)

    const titles: { label: string; value: keyof IParcelaDescontoFinanceiroDTO | 'acao' }[] = [
        { label: 'Parcela', value: 'numeroParcela' },
        { label: 'Competência', value: 'competencia' },
        { label: 'Valor da parcela de desconto', value: 'valor' },
        { label: 'Valor descontado', value: 'valorRealizado' },
        { label: 'Status Parcela', value: 'status' },
        { label: '', value: 'acao' }
    ]

    // function imprimirDesconto() {
    //     DescontosFinanceiros.getPdfByUuiD(id)
    //         .then((response) => {
    //             const url = window.URL.createObjectURL(
    //                 new Blob([response.data], {
    //                     type: response?.headers['content-type']
    //                 })
    //             )
    //             // console.log(url)
    //             setFileUrl(url)
    //             // downloadFileV2(response.data, fileNameFromBlob(response?.headers['content-type']))
    //         })
    //         .catch(() => {
    //             addToast({ title: 'Erro ao fazer download', type: 'error', duration: 3000 })
    //         })
    // }

    function downloadFile(documentoId?: string, docName?: string) {
        if (documentoId) {
            ConsultaDocumentosController.getDocDownload(documentoId)
                .then((response) => {
                    const fileName = fileNameFromBlob(response?.headers['content-disposition'])
                    downloadFileV2(response?.data, fileName ? fileName : docName)
                })
                .catch((err) => {
                    addToast({
                        title: err?.response?.data?.message ? err?.response?.data?.message : 'Ocorreu um erro: Desconhecido',
                        type: 'error',
                        duration: 5000
                    })
                })
        }
    }

    function createUrlDisplay(documentoId?: string) {
        if (documentoId) {
            ConsultaDocumentosController.getDocView(documentoId)
                .then((response) => {
                    const url = window.URL.createObjectURL(
                        new Blob([response.data], {
                            type: response?.headers['content-type']
                        })
                    )

                    setFileUrl(url)
                })
                .catch((err) => {
                    addToast({
                        title: err?.response?.data?.message ? err?.response?.data?.message : 'Ocorreu um erro: Desconhecido',
                        type: 'error',
                        duration: 5000
                    })
                })
        }
    }

    function handlePrint(id: string) {
        window.open(`/contas/pdf?id=${id}&tipoPDF=DESCONTO_FINANCEIRO`), '_black'
    }

    useEffect(() => {
        const { id } = route.query
        if (!id) return
        setPageId(id)
    }, [route.isReady])

    useEffect(() => {
        if (id) {
            // imprimirDesconto()
            DescontosFinanceiros.getByID({ descontoId: id }).then(({ data }) => setDataDiscount(data))
        }
    }, [id])

    return (
        <S.BodyContainer>
            <SectionContainer>
                <Grid container rowGap="32px">
                    <Grid xs={6}>
                        <S.InfoWrapper>
                            <span>Prestador</span>
                            <p>{`${maskCNPJ(dataDiscount.cnpjPrestador)} - ${dataDiscount.nomePrestador}`}</p>
                        </S.InfoWrapper>
                    </Grid>
                    <Grid xs={3}>
                        <S.InfoWrapper>
                            <span>Tipo de Desconto</span>
                            <p>{tipoDescontoEnum[dataDiscount.tipo]}</p>
                        </S.InfoWrapper>
                    </Grid>
                    <Grid xs={3}>
                        <S.InfoWrapper>
                            <span>Descrição</span>
                            <p>{dataDiscount.descricao}</p>
                        </S.InfoWrapper>
                    </Grid>

                    <Grid xs={3}>
                        <S.InfoWrapper>
                            <span>Valor do Desconto</span>
                            <p>{maskMon(dataDiscount?.valorMeta?.toFixed(2))}</p>
                        </S.InfoWrapper>
                    </Grid>
                    <Grid xs={3}>
                        <S.InfoWrapper>
                            <span>Saldo a descontar</span>
                            <p>{maskMon(dataDiscount?.valorPendente?.toFixed(2))}</p>
                        </S.InfoWrapper>
                    </Grid>
                    <Grid xs={dataDiscount?.tipo === 'PERCENTUAL' ? 3 : 6}>
                        <S.InfoWrapper>
                            <span>Número de Parcelas</span>
                            <p>{dataDiscount.numeroParcelas}</p>
                        </S.InfoWrapper>
                    </Grid>
                    {dataDiscount?.tipo === 'PERCENTUAL' && (
                        <Grid xs={3}>
                            <S.InfoWrapper>
                                <span>Percentual</span>
                                <p>{dataDiscount?.percentual}%</p>
                            </S.InfoWrapper>
                        </Grid>
                    )}

                    <Grid xs={3}>
                        <S.InfoWrapper>
                            <span>Competência de início do desconto</span>
                            <p>{parseDataToMMYYYY(dataDiscount?.competenciaInicioDesconto, 'MM/YYYY')}</p>
                        </S.InfoWrapper>
                    </Grid>
                    <Grid xs={3}>
                        <S.InfoWrapper>
                            <span>Situação</span>
                            <p>{statusDescontoEnum[dataDiscount.status]}</p>
                        </S.InfoWrapper>
                    </Grid>
                    <Grid xs={3}>
                        <S.InfoWrapper>
                            <span>Número do processo SEI</span>
                            <p>{dataDiscount.numeroProcessoSei || '- - -'}</p>
                        </S.InfoWrapper>
                    </Grid>
                    <Grid xs={3}>
                        <S.InfoWrapper>
                            <span>Número do processo judicial</span>
                            <p>{dataDiscount.numeroProcessoJudicial || '- - -'}</p>
                        </S.InfoWrapper>
                    </Grid>
                    <div style={{ display: 'grid', gridTemplateColumns: '4.6fr 1fr' }}>
                        <Grid xs={12}>
                            <S.InfoWrapper>
                                <span>Observações</span>
                                <p>{dataDiscount?.observacoes}</p>
                            </S.InfoWrapper>
                        </Grid>
                        <Grid xs={12}>
                            <S.InfoWrapper>
                                <span>Justificativa</span>
                                <p>{dataDiscount?.justificativa}</p>
                            </S.InfoWrapper>
                        </Grid>
                    </div>
                </Grid>
            </SectionContainer>

            <S.BoxContent>
                {/* ArquivosListMock */}
                {/* dataDiscount?.documentos */}

                {dataDiscount?.documentos?.length > 0 &&
                    dataDiscount?.documentos?.map((item, index) => (
                        <S.Archive key={index}>
                            <div
                                onClick={() => {
                                    downloadFile(item?.documentoId, item?.nome)
                                }}
                            >
                                <ReactSVG wrapper="span" title="Baixar arquivo" src="/contas/assets/icons/download_blue.svg" />
                                <p>{item?.nome}</p>
                            </div>
                            <div>
                                <ReactSVG
                                    wrapper="span"
                                    src="/contas/assets/icons/eye2.svg"
                                    onClick={() => {
                                        createUrlDisplay(item?.documentoId)
                                        setFileModal(true)
                                    }}
                                    title="Visualizar arquivo"
                                />
                            </div>
                        </S.Archive>
                    ))}
            </S.BoxContent>

            {dataDiscount?.parcelas?.length > 0 ? (
                <>
                    <div>
                        <S.ListagemPrestadores>
                            <p>Parcelas</p>
                        </S.ListagemPrestadores>
                        <TablePagination
                            titles={titles}
                            values={dataDiscount?.parcelas?.map((item) => {
                                return {
                                    ...item,
                                    competencia: parseDataToMMYYYY(item?.competencia, 'MM/YYYY'),
                                    valor: moneyParser({ valor: item?.valor }),
                                    valorRealizado: moneyParser({ valor: item?.valorRealizado }),
                                    status: (
                                        <div style={{ width: 'fit-content' }}>
                                            <Badge
                                                color={BadgesColorEnum[item?.status]}
                                                background={BadgesBackgroundEnum[item?.status]}
                                                text={BadgesTextEnum[item?.status]}
                                            />
                                        </div>
                                    )
                                }
                            })}
                            pagination={pagination}
                            customGridStyles="1fr 1fr 1fr 1fr 1fr 0.1fr"
                        />
                    </div>
                </>
            ) : (
                <NoItensComponent src="/contas/assets/imgs/notfound.svg" titulo="Sem parcelas disponíveis" />
            )}

            <S.ButtonActions>
                {/* ==========TIPO 1============ */}
                {/* <a target="_blank" href={`${process.env.NEXT_PUBLIC_API_CONTAS_PAGAR}descontos-financeiros/${id}/pdf`} rel="noreferrer">
                    <Button color="primary" variant="text" startIcon={<ReactSVG src="/contas/assets/imgs/mai-ic-export-file.mono.svg" />}>
                        Imprimir
                    </Button>
                </a> */}

                {/* ==========TIPO 2============ */}
                {/* <a target="_blank" href={fileUrl} download rel="noreferrer">
                    <Button color="primary" variant="text" startIcon={<ReactSVG src="/contas/assets/imgs/mai-ic-export-file.mono.svg" />}>
                        Imprimir
                    </Button>
                </a> */}

                {/* ==========TIPO 3============ */}
                <Button
                    onClick={() => handlePrint(pageId)}
                    color="primary"
                    variant="text"
                    startIcon={<ReactSVG src="/contas/assets/imgs/mai-ic-export-file.mono.svg" />}
                >
                    Imprimir
                </Button>
                <Button color="neutral" variant="text" onClick={() => route.back()}>
                    Voltar
                </Button>
            </S.ButtonActions>

            <AttachmentViewModal isModalOpen={fileModal} onCloseModel={() => setFileModal(false)} documentUrl={fileUrl} dragButtonModal={true} />
        </S.BodyContainer>
    )
}

export default VisualizarDescontoTemplate
