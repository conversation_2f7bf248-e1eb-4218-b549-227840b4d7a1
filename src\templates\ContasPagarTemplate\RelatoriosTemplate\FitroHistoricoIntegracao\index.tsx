/* eslint-disable react-hooks/exhaustive-deps */
import Selectable from 'components/molecules/Select'
import { Dispatch, SetStateAction, useEffect, useState } from 'react'
import { IDataFilterRelatorio } from './filtro-types'

import { selectTipoPrestadorOptions } from './mocksSelec'

import { FormControl, InputLabel, MenuItem, Select, TextField } from '@mui/material'
import AsyncSimpleSelect from 'components/atoms/AsyncSimpleSelect'
import Checkbox from 'components/molecules/Checkbox'
import { Competencia } from 'src/services/ContasPagar/competencia-controller'
import { Relatorios } from 'src/services/ContasPagar/relatorio-controller'
import { SituacaoPagamentoEnum } from 'src/types/enums'
import { parseDataToMMYYYY } from 'utils/functions'
import { ParseTipoRelatorioEnum, TipoRelatorioEnum } from './enums'
import * as S from './styles'

type FitroRetornoPED = {
    dataFilter: IDataFilterRelatorio
    setDataFilter: Dispatch<SetStateAction<IDataFilterRelatorio>>
    buscar: (data: any) => void
}

type typeOptions = {
    label: string
    value: string
}

export type AsyncSelectOptionsTypes = {
    value: string
    label: string
}

export interface ISelectProps<T = any> {
    label: string
    value: string
    element?: T
}

const situacaoPagamentoOptions = Object.values(SituacaoPagamentoEnum).map((situacao) => ({ value: situacao, label: situacao.replace(/_/gi, ' ') }))

const FitroRetornoPED = ({ dataFilter, setDataFilter, buscar }: FitroRetornoPED) => {
    const [competenciaOptions, setCompetenciaOptions] = useState<typeOptions[]>([])
    const [tipoRelatorioOptions, setTipoRelatorioOptions] = useState<typeOptions[]>([])

    const [municipio, setMunicipio] = useState<typeOptions>()
    const [prestador, setPrestador] = useState<typeOptions>()
    const [prestadores, setPrestadores] = useState<ISelectProps<any>[]>([])

    function getCompetencias() {
        Competencia.get().then(({ data }) => {
            const competenciaOptions: typeOptions[] = data?.map((item) => ({
                label: parseDataToMMYYYY(item?.competencia?.toString(), 'MM/YYYY'),
                value: item?.competencia?.toString()
            }))
            setCompetenciaOptions(competenciaOptions)
        })
    }

    function getTiposRelatorios() {
        Relatorios.getDisponiveis().then(({ data }) => {
            const tipoRelatorioOptions: typeOptions[] = data?.map((item) => ({
                label: ParseTipoRelatorioEnum[item],
                value: item
            }))
            setTipoRelatorioOptions(tipoRelatorioOptions)
        })
    }

    useEffect(() => {
        getCompetencias()
        getTiposRelatorios()
        // if (dataFilter?.competencia) {
        //     getRelatorioFinal()
        // }
    }, [dataFilter?.competencia])

    const parserMunicipio = (props): AsyncSelectOptionsTypes[] => {
        return props.map((item) => ({
            value: item?.codigoIbge,
            label: item?.municipio
        }))
    }

    const parserPrestador = (props): AsyncSelectOptionsTypes[] => {
        return props.map((item) => ({
            value: item.prestadorId,
            label: item.nome
        }))
    }

    const loadOptionsPrestador = async (str: string): Promise<AsyncSelectOptionsTypes[]> => {
        return (
            str &&
            Relatorios.getPrestador({
                nomeCNPJ: str,
                tipoRelatorio: dataFilter?.tipoRelatorio
            }).then(({ data }) => {
                return parserPrestador(data)
            })
        )
    }

    // useEffect(() => {
    //     setDataFilter((prev) => ({ ...prev, tipoPrestador: null, competencia: null }))
    // }, [dataFilter?.tipoRelatorio])

    function handleAddPrestador(prestadorSelected: ISelectProps) {
        const alreadExists = prestadores?.some((item) => item?.value === prestadorSelected?.value)
        if (!prestadorSelected?.value || alreadExists) return
        setPrestadores((prev) => [...prev, prestadorSelected])
    }

    function handleDeletePrestador(prestadorDelete: ISelectProps) {
        const indexToDelete = prestadores.findIndex((item) => item.value === prestadorDelete?.value)

        if (indexToDelete !== -1) {
            const novosPrestadores = prestadores.filter((_, index) => index !== indexToDelete)

            setPrestadores(novosPrestadores)

            setDataFilter((prev) => ({
                ...prev,
                prestadoresList: novosPrestadores
            }))
        }
    }

    useEffect(() => {
        if (dataFilter?.prestadoresList && dataFilter.prestadoresList.length > 0 && prestadores.length === 0) {
            setPrestadores(dataFilter.prestadoresList)
        }
    }, [dataFilter?.prestadoresList, prestadores.length])

    useEffect(() => {
        if (prestadores.length > 0) {
            setDataFilter((prev) => ({
                ...prev,
                prestadoresList: prestadores
            }))
        } else if (prestadores.length === 0) {
            setDataFilter((prev) => ({
                ...prev,
                prestadoresList: []
            }))
        }
    }, [prestadores])

    useEffect(() => {
        if (dataFilter?.prestadoresList === undefined && prestadores.length > 0) {
            setPrestadores([])
        }
    }, [dataFilter?.prestadoresList])

    //

    // Load de options de municipios
    const loadOptionsMunicipio = async (str: string): Promise<AsyncSelectOptionsTypes[]> => {
        return (
            str &&
            Relatorios.getMunicipio(str).then(({ data }) => {
                return parserMunicipio(data)
            })
        )
    }

    // Função debounce
    const debounce = (func: (...args: string[]) => void, delay: number) => {
        let timeoutId: NodeJS.Timeout
        return (...args: string[]): Promise<{ value: string; label: string }[]> => {
            return new Promise((resolve) => {
                clearTimeout(timeoutId)
                timeoutId = setTimeout(() => {
                    resolve(func.apply(this, args))
                }, delay)
            })
        }
    }

    const handleChange = (value: string) => {
        debouncedTextHandler(value)
    }

    const debouncedTextHandler = debounce(async (text: string): Promise<{ value: string; label: string }[]> => {
        const options = await loadOptionsMunicipio(text)
        return options
    }, 1000)

    return (
        <S.BodyContainer>
            <S.FilterRow0>
                <FormControl>
                    <InputLabel required>Tipo de Relatório</InputLabel>
                    <Select
                        defaultValue={dataFilter?.tipoRelatorio}
                        onChange={(e) => {
                            setDataFilter({
                                ...dataFilter,
                                tipoRelatorio: e?.target?.value as TipoRelatorioEnum
                            })
                        }}
                    >
                        {tipoRelatorioOptions?.map((item, index) => {
                            return (
                                <MenuItem key={index} value={item?.value}>
                                    {item?.label}
                                </MenuItem>
                            )
                        })}
                    </Select>
                </FormControl>
            </S.FilterRow0>
            <S.FilterRow1>
                <FormControl>
                    <InputLabel>Competência</InputLabel>
                    <Select
                        defaultValue={dataFilter?.competencia}
                        // key={dataFilter?.tipoRelatorio}
                        // value={dataFilter?.competencia}
                        required
                        onChange={(e) => {
                            setDataFilter({
                                ...dataFilter,
                                competencia: e?.target?.value
                            })
                        }}
                    >
                        {competenciaOptions?.map((item, index) => {
                            return (
                                <MenuItem key={index} value={item?.value}>
                                    {item?.label}
                                </MenuItem>
                            )
                        })}
                    </Select>
                </FormControl>

                <S.SearchWrapper>
                    {/* <PrestadorInput handleOnChange={(e) => setDataFilter({ ...dataFilter, prestadorUUID: e?.uuid })} /> */}
                    <AsyncSimpleSelect
                        label="Prestador"
                        disabled={!dataFilter?.tipoRelatorio}
                        value={prestador}
                        defaultValue={() => ''}
                        key={`${prestador}`}
                        onChange={(e) => {
                            handleAddPrestador(e)
                            setPrestador(null)
                        }}
                        isClearable
                        loadOptions={loadOptionsPrestador}
                    />{' '}
                </S.SearchWrapper>
            </S.FilterRow1>
            <S.WrapperPrestadores>
                {prestadores?.map((item, index) => (
                    <p key={index}>
                        {item?.label}
                        <span onClick={() => handleDeletePrestador(item)}>x</span>
                    </p>
                ))}
            </S.WrapperPrestadores>

            {dataFilter?.tipoRelatorio === 'ACOMPANHAMENTO_PAGAMENTO' ? (
                <>
                    <S.FilterRow2>
                        <Selectable
                            value={situacaoPagamentoOptions.find((item) => item.value === dataFilter?.situacaoPagamento)}
                            label="Situação do pagamento"
                            // options={selectSituacaoPagamentoOptions}
                            options={situacaoPagamentoOptions}
                            onChange={(e) => {
                                setDataFilter({
                                    ...dataFilter,
                                    situacaoPagamento: SituacaoPagamentoEnum[e?.value]
                                    // situacaoPagamento: { value: e.label === 'Ativo' ? true : false, label: e.label }
                                })
                            }}
                        />
                        <TextField
                            label="Data inicial geração Demonstrativo"
                            type="date"
                            value={dataFilter?.dataInicialDemonstrativo}
                            onChange={(e) => {
                                setDataFilter({
                                    ...dataFilter,
                                    dataInicialDemonstrativo: e.target.value
                                })
                            }}
                        />
                        <TextField
                            label="Data final geração Demonstrativo"
                            type="date"
                            value={dataFilter?.dataFinalDemonstrativo}
                            onChange={(e) => {
                                setDataFilter({
                                    ...dataFilter,
                                    dataFinalDemonstrativo: e.target.value
                                })
                            }}
                        />
                    </S.FilterRow2>
                </>
            ) : dataFilter?.tipoRelatorio === 'EXTRATO_PRODUCAO' ? (
                <>
                    <S.FilterRow2_1>
                        <Selectable
                            value={dataFilter?.tipoPrestador}
                            label="Tipo de Prestador"
                            options={selectTipoPrestadorOptions}
                            onChange={(e) => {
                                setDataFilter({
                                    ...dataFilter,
                                    tipoPrestador: e
                                    // tipoPrestador: { value: e.label === 'Ativo' ? true : false, label: e.label }
                                })
                            }}
                        />
                    </S.FilterRow2_1>
                </>
            ) : dataFilter?.tipoRelatorio === 'LIQUIDACOES' ? (
                <>
                    <S.FilterRow3_1>
                        <TextField
                            label="Data inicial geração LIQ"
                            type="date"
                            value={dataFilter?.dataInicialGeracaoLiq}
                            onChange={(e) => {
                                setDataFilter({
                                    ...dataFilter,
                                    dataInicialGeracaoLiq: e.target.value
                                })
                            }}
                        />
                        <TextField
                            label="Data final geração LIQ"
                            type="date"
                            value={dataFilter?.dataFinalGeracaoLiq}
                            onChange={(e) => {
                                setDataFilter({
                                    ...dataFilter,
                                    dataFinalGeracaoLiq: e.target.value
                                })
                            }}
                        />
                        <TextField
                            label="Data inicial do pagamento"
                            type="date"
                            value={dataFilter?.dataInicialPagamento}
                            onChange={(e) => {
                                setDataFilter({
                                    ...dataFilter,
                                    dataInicialPagamento: e.target.value
                                })
                            }}
                        />
                        <TextField
                            label="Data final do pagamento"
                            type="date"
                            value={dataFilter?.dataFinalPagamento}
                            onChange={(e) => {
                                setDataFilter({
                                    ...dataFilter,
                                    dataFinalPagamento: e.target.value
                                })
                            }}
                        />
                    </S.FilterRow3_1>
                </>
            ) : dataFilter?.tipoRelatorio === 'PRODUCAO_APROVADA' ? (
                <>
                    <S.FilterRow2_1>
                        <Selectable
                            value={dataFilter?.tipoPrestador}
                            label="Tipo de Prestador"
                            required
                            options={selectTipoPrestadorOptions}
                            onChange={(e) => {
                                setDataFilter({
                                    ...dataFilter,
                                    tipoPrestador: e
                                    // tipoPrestador: { value: e.label === 'Ativo' ? true : false, label: e.label }
                                })
                            }}
                        />

                        <TextField
                            label="Data inicial da aprovação"
                            type="date"
                            value={dataFilter?.dataAprovacaoInicial}
                            onChange={(e) => {
                                setDataFilter({
                                    ...dataFilter,
                                    dataAprovacaoInicial: e.target.value
                                })
                            }}
                        />
                        <TextField
                            label="Data final da aprovação"
                            type="date"
                            value={dataFilter?.dataAprovacaoFinal}
                            onChange={(e) => {
                                setDataFilter({
                                    ...dataFilter,
                                    dataAprovacaoFinal: e.target.value
                                })
                            }}
                        />
                    </S.FilterRow2_1>
                </>
            ) : dataFilter?.tipoRelatorio === 'RETENCOES_ISS' ? (
                <>
                    <S.FilterRow3>
                        <TextField
                            label="Data inicial do pagamento"
                            type="date"
                            value={dataFilter?.dataInicialPagamento}
                            onChange={(e) => {
                                setDataFilter({
                                    ...dataFilter,
                                    dataInicialPagamento: e.target.value
                                })
                            }}
                        />
                        <TextField
                            label="Data final do pagamento"
                            type="date"
                            value={dataFilter?.dataFinalPagamento}
                            onChange={(e) => {
                                setDataFilter({
                                    ...dataFilter,
                                    dataFinalPagamento: e.target.value
                                })
                            }}
                        />
                        {/* <TextField
                            value={dataFilter?.municipio}
                            label="Municipio"
                            onChange={(e) => {
                                setDataFilter({
                                    ...dataFilter,
                                    municipio: e?.target?.value
                                })
                            }}
                        /> */}
                        <AsyncSimpleSelect
                            label="Municipio"
                            value={municipio}
                            defaultValue={() => dataFilter?.codigoIbgeMunicipio}
                            onChange={(e) => {
                                setMunicipio(e)
                                setDataFilter({
                                    ...dataFilter,
                                    codigoIbgeMunicipio: e?.value,
                                    municipioNome: e?.label
                                })
                            }}
                            onInputChange={(value: string) => handleChange(value)}
                            isClearable
                            loadOptions={debouncedTextHandler}
                        />{' '}
                    </S.FilterRow3>
                    <S.FilterRow3>
                        <Checkbox
                            borderColor="rgba(0, 0, 0, 0.88)"
                            color="#2B45D4"
                            label="vencidas"
                            key={'titulosvencidos'}
                            checked={dataFilter.separarMunicipios}
                            onChange={() => {
                                setDataFilter({
                                    ...dataFilter,
                                    separarMunicipios: !dataFilter.separarMunicipios
                                })
                            }}
                        >
                            <S.Text>Separar municípios em páginas</S.Text>
                        </Checkbox>
                    </S.FilterRow3>
                </>
            ) : null}
        </S.BodyContainer>
    )
}

export default FitroRetornoPED

