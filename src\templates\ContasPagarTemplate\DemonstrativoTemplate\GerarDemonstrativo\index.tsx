/* eslint-disable react-hooks/exhaustive-deps */
import { Box, Button, CircularProgress } from '@mui/material'
import NoItensComponent from 'components/atoms/NoItensComponent'
import TablePagination from 'components/molecules/TablePagination'
import SomaPrestadores from 'components/organisms/SomaPrestadoresComponent'
import { useEffect, useState } from 'react'
import { useFilterPersistence } from 'hooks/storage/useFilterPersistence'
import { Prestador } from 'src/services/ContasPagar/prestador-controller'
import { TipoPrestadorEnum } from 'src/services/ContasPagar/prestador-controller/enuns'
import { IGetListagemPrestadores } from 'src/services/ContasPagar/prestador-controller/prestador-controller-type'
import { IPagination } from 'src/types/pagination'
import { capitalize, parseDataToMMYYYY } from 'utils/functions'
import { maskMon } from 'utils/masks'
import { PaginationHelper } from 'utils/pagination-helper'
import { IDataFilterGerarDemonstrativo } from '../Filtros/filtro-types'
import FitroGerarDemonstrativo from '../Filtros/FIltroGerarDemonstrativo'
import FitroGerarPED from '../Filtros/FIltroGerarDemonstrativo'
import ConfirmarDemonstrativoModal from '../ModaisDemonstrativos/ModalGerarArquivo'
import * as S from './styles'
import { useToast } from 'hooks/toast'
import { maskCNPJ } from 'utils/masks/formatCNPJ'
import { maskArquive } from 'utils/masks/formatArquive'

interface IPersistedFilters extends IDataFilterGerarDemonstrativo {
    numberPage: number
    prestadoresSelecionados: IGetListagemPrestadores[]
}

const initialGerarPEDFilterValue: IDataFilterGerarDemonstrativo = {
    ...FitroGerarPED,
    tipo: 'CREDENCIADO'
}

type loadDadosType = {
    zerarDados?: boolean
    competenciaInicial?: string
}

const GerarDemonstrativoComponent = () => {
    const { addToast } = useToast()

    const {
        filters: persistedFilters,
        updateFilters: updatePersistedFilters,
        clearFilters: clearPersistedFilters
    } = useFilterPersistence({
        route: '/demonstrativo',
        filterType: 'gerar-demonstrativo',
        defaultValues: {
            ...initialGerarPEDFilterValue,
            numberPage: 0,
            prestadoresSelecionados: []
        }
    })

    const typedPersistedFilters = persistedFilters as IPersistedFilters | null

    const [loadingDados, setLoadingDados] = useState(false)
    const [dataFilterGerarDemonstrativo, setDataFilterGerarDemonstrativo] = useState<IDataFilterGerarDemonstrativo>(() => {
        if (typedPersistedFilters && Object.keys(typedPersistedFilters).length > 0) {
            return typedPersistedFilters
        }
        return initialGerarPEDFilterValue
    })

    const [pagination, setPagination] = useState<IPagination>()
    const [numberPage, setNumberPage] = useState<number>(typedPersistedFilters?.numberPage || 0)

    const updatePage = (newPage: number) => {
        setNumberPage(newPage)
        if (dataFilterGerarDemonstrativo && Object.keys(dataFilterGerarDemonstrativo).length > 0) {
            updatePersistedFilters({
                ...dataFilterGerarDemonstrativo,
                numberPage: newPage,
                prestadoresSelecionados
            })
        }
    }
    const [prestadoresList, setPrestadoresList] = useState<IGetListagemPrestadores[]>()
    const [prestadoresSelecionados, setPrestadoresSelecionados] = useState<IGetListagemPrestadores[]>(
        typedPersistedFilters?.prestadoresSelecionados || []
    )
    const [openModal, setOpenModal] = useState(false)
    const [totalPrestadores, setTotalPrestadores] = useState<number>()

    function loadDados({ zerarDados, competenciaInicial }: loadDadosType) {
        setLoadingDados(true)
        if (zerarDados) {
            setPrestadoresSelecionados([])
            clearPersistedFilters()
        }
        const regexCnpj = /^\d{2}\.\d{3}\.\d{3}\/\d{4}-\d{2}$/
        const isCnpj = regexCnpj.test(dataFilterGerarDemonstrativo?.prestador)
        Prestador.get({
            competencia: competenciaInicial ? competenciaInicial : dataFilterGerarDemonstrativo?.competencia,
            tipoPrestador: dataFilterGerarDemonstrativo?.tipo,
            nomeCNPJCodigo: isCnpj ? dataFilterGerarDemonstrativo?.prestador?.replace(/[^\d]+/g, '') : dataFilterGerarDemonstrativo?.prestador,
            municipios: dataFilterGerarDemonstrativo?.municipios,
            segregarMunicipios: dataFilterGerarDemonstrativo?.segregarMunicipios,
            status: 'AGUARDANDO_GERACAO_DEMOSTRATIVO',
            page: numberPage,
            size: 10
        })
            .then(({ data }) => {
                setLoadingDados(false)
                setPrestadoresList(data?.content)
                setPagination(PaginationHelper.parserPagination(data, updatePage))
                setTotalPrestadores(data?.totalElements)
            })
            .catch((err) => {
                addToast({
                    title: err?.response?.data?.message ? err?.response?.data?.message : 'Ocorreu um erro: Desconhecido',
                    type: 'error',
                    duration: 5000
                })
            })
    }

    const titles: { label: string; value: keyof IGetListagemPrestadores | 'acao' }[] = [
        { label: 'Nome do prestador', value: 'nomePrestador' },
        { label: 'Competência', value: 'competencia' },
        { label: 'PED', value: 'numeroPed' },
        { label: 'EMP', value: 'numeroEmp' },
        { label: 'Valor bruto', value: 'valorBruto' },
        { label: '', value: 'acao' }
    ]

    useEffect(() => {
        if (dataFilterGerarDemonstrativo?.competencia && numberPage !== undefined) {
            loadDados({})
        }
    }, [numberPage])

    useEffect(() => {
        if (typedPersistedFilters && typedPersistedFilters.competencia && !prestadoresList) {
            loadDados({})
        }
    }, [typedPersistedFilters])

    useEffect(() => {
        if (dataFilterGerarDemonstrativo && Object.keys(dataFilterGerarDemonstrativo).length > 0) {
            updatePersistedFilters({
                ...dataFilterGerarDemonstrativo,
                numberPage,
                prestadoresSelecionados
            })
        }
    }, [dataFilterGerarDemonstrativo, prestadoresSelecionados])

    const handleLimparFiltros = () => {
        // Manter a competência atual e limpar os outros filtros
        const competenciaAtual = dataFilterGerarDemonstrativo?.competencia
        const filtrosLimpos = {
            ...initialGerarPEDFilterValue,
            competencia: competenciaAtual,
            prestador: undefined,
            municipios: undefined,
            segregarMunicipios: undefined
        }

        setDataFilterGerarDemonstrativo(filtrosLimpos)
        setNumberPage(0)
        setPrestadoresList([])
        setPrestadoresSelecionados([])

        // Limpar sessionStorage
        try {
            const keys = ['@filters:/demonstrativo:gerar-demonstrativo', '@filters:/demonstrativo:gerar-demonstrativo-visual']
            keys.forEach((key) => sessionStorage.removeItem(key))
        } catch (error) {
            console.error('Erro ao limpar sessionStorage:', error)
        }

        // Limpar filtros persistidos
        clearPersistedFilters()
    }

    return (
        <>
            <FitroGerarDemonstrativo
                dataFilter={dataFilterGerarDemonstrativo}
                setDataFilter={setDataFilterGerarDemonstrativo}
                buscar={(e) => {
                    setNumberPage(0)
                    loadDados({ competenciaInicial: e })
                }}
            />

            {dataFilterGerarDemonstrativo &&
                Object.keys(dataFilterGerarDemonstrativo).length > 0 &&
                (dataFilterGerarDemonstrativo.prestador || dataFilterGerarDemonstrativo.municipios?.length > 0) && (
                    <div style={{ marginBottom: '16px', marginRight: '20px', textAlign: 'right' }}>
                        <Button variant="outlined" color="secondary" onClick={handleLimparFiltros}>
                            Limpar Filtros
                        </Button>
                    </div>
                )}

            {loadingDados ? (
                <Box sx={{ display: 'flex', width: '100%', justifyContent: 'center', alignItems: 'center', height: '70vh' }}>
                    <CircularProgress />
                </Box>
            ) : (
                <>
                    {prestadoresList?.length > 0 ? (
                        <>
                            {' '}
                            <div>
                                <S.ListagemPrestadores>
                                    <p>Prestadores</p>
                                </S.ListagemPrestadores>
                                {prestadoresList?.length > 0 && (
                                    <TablePagination
                                        titles={titles}
                                        values={prestadoresList?.map((item) => {
                                            return {
                                                ...item,
                                                nomePrestador: (
                                                    <S.PrestadorInfo>
                                                        <p>{capitalize(item?.nomePrestador)}</p>
                                                        <span>
                                                            CNPJ:{maskCNPJ(item?.cnpjPrestador)}, Tipo:{TipoPrestadorEnum[item?.tipoPrestador]}
                                                        </span>
                                                    </S.PrestadorInfo>
                                                ),
                                                competencia: parseDataToMMYYYY(item?.competencia?.toString(), 'MM/YYYY'),
                                                numeroPed: <p>{item?.numeroPed && maskArquive(item?.numeroPed)}</p>,
                                                numeroEmp: <p>{item?.numeroEmp && maskArquive(item?.numeroEmp)}</p>,
                                                valorBruto: maskMon(item?.valorBruto.toFixed(2))
                                            }
                                        })}
                                        pagination={pagination}
                                        enableSelect
                                        handleSelect={setPrestadoresSelecionados}
                                        customGridStyles="1.8fr 0.6fr 1fr 1fr 0.8fr 0.1fr"
                                        checkSelected={prestadoresSelecionados}
                                        noSelectAll={true}
                                        selectIdField={'idPrestador'}
                                    />
                                )}
                            </div>
                            <SomaPrestadores
                                prestadoresList={true}
                                prestadoresSelecionados={prestadoresSelecionados}
                                setPrestadoresSelecionados={setPrestadoresSelecionados}
                                setOpenModalDemonstrativo={setOpenModal}
                                buttonIconLeft="/contas/assets/imgs/mai-ic-export-file.mono-black.svg"
                                totalElements={totalPrestadores || prestadoresList?.length}
                            />
                            <ConfirmarDemonstrativoModal
                                quantPrestadoresTotal={totalPrestadores}
                                openModal={openModal}
                                setOpenModal={setOpenModal}
                                prestadoresSelecionados={prestadoresSelecionados}
                                tipo={dataFilterGerarDemonstrativo?.tipo}
                                competencia={dataFilterGerarDemonstrativo?.competencia}
                                reloadData={() => loadDados({ zerarDados: true })}
                                codigosMunicipios={dataFilterGerarDemonstrativo?.municipios}
                                segregarMunicipios={dataFilterGerarDemonstrativo?.segregarMunicipios}
                            />
                        </>
                    ) : (
                        <NoItensComponent
                            src="/contas/assets/imgs/notfound.svg"
                            titulo="Ops!... nenhum resultado encontrado"
                            subTitulo="Tente outra pesquisa"
                        />
                    )}
                </>
            )}
        </>
    )
}

export default GerarDemonstrativoComponent

