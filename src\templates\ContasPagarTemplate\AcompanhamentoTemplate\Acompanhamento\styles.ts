import styled from 'styled-components'

export const ListagemPrestadores = styled.div`
    display: flex;
    flex-direction: column;
    gap: 24px;
    align-items: flex-start;
    padding: 24px;
    background: #ffffff;
    border-radius: 8px;

    p {
        font-family: 'Open Sans';
        font-style: normal;
        font-weight: 600;
        font-size: 16px;
        line-height: 24px;
        text-transform: capitalize;
        color: #2b45d4;
    }
`

export const Wrapper = styled.div`
    width: 100%;
    background: #ffffff;
    border-radius: 8px;
`

export const InformacoesGerais = styled.div`
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    padding: 24px;
    gap: 24px;
    background: #ffffff;
    border-radius: 8px;

    h4 {
        font-family: 'Open Sans';
        font-style: normal;
        font-weight: 600;
        font-size: 16px;
        line-height: 24px;
        text-align: center;
        color: #2b45d4;
    }
`

export const NomePrestador = styled.div`
    display: flex;
    flex-direction: column;

    p {
        font-family: 'Open Sans';
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 24px;
        display: flex;
        align-items: center;
        color: rgba(0, 0, 0, 0.88);
    }

    span {
        font-family: 'Open Sans';
        font-style: normal;
        font-weight: 400;
        font-size: 12px;
        line-height: 16px;
        display: flex;
        align-items: center;
        color: rgba(0, 0, 0, 0.56);
    }
`
export const LinkWrapper = styled.div`
    display: flex;
    cursor: pointer;
`
export const ButtonAction = styled.div`
    width: 100%;
    display: flex;
    justify-content: end;
    margin: 32px 0;
`
