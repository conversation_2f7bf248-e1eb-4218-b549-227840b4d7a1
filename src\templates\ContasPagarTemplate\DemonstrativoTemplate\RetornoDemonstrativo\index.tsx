/* eslint-disable react-hooks/exhaustive-deps */
import { Box, Button, CircularProgress, Popover, Typography } from '@mui/material'
import HorizontalLine from 'components/atoms/HorizaontalLine'
import NoItensComponent from 'components/atoms/NoItensComponent'
import TablePagination from 'components/molecules/TablePagination'
import SomaPrestadores from 'components/organisms/SomaPrestadoresComponent'
import UserContext from 'context/UserContext/UserContext'
import { useFilterPersistence } from 'hooks/storage/useFilterPersistence'
import { useToast } from 'hooks/toast'
import { useRouter } from 'next/router'
import { useContext, useEffect, useState } from 'react'
import { ReactSVG } from 'react-svg'
import { ProfilesEnum } from 'src/enum/profiles.enum'
import { Demonstrativo } from 'src/services/ContasPagar/demonstrativo'
import { IGeDemonstrativo } from 'src/services/ContasPagar/demonstrativo/contas-type'
import { TipoPrestadorEnum } from 'src/services/ContasPagar/prestador-controller/enuns'
import { IPagination } from 'src/types/pagination'
import { maskMon } from 'utils/masks'
import { maskArquive } from 'utils/masks/formatArquive'
import { maskCNPJ } from 'utils/masks/formatCNPJ'
import { PaginationHelper } from 'utils/pagination-helper'
import { IDataFilterRetornoDemonstrativo } from '../Filtros/filtro-types'
import FitroRetornoDemonstrativo from '../Filtros/FIltroConsultaDemonstrativo'
import BloquearNFPrestadorModal from '../ModaisDemonstrativos/ModalBloquearNFPrestador'
import CancelarDemonstrativoModal from '../ModaisDemonstrativos/ModalCancelarDemonstrativos'
import LiberarDemonstrativoModal from '../ModaisDemonstrativos/ModalLiberarDemonstrativos'
import LiberarPrestadorModal from '../ModaisDemonstrativos/ModalLiberarPrestador'
import * as S from './styles'

const RetornoDemonstrativoComponent = () => {
    const route = useRouter()
    const { addToast } = useToast()

    // Hook para persistência dos filtros e paginação
    const {
        filters: persistedFilters,
        updateFilters: updatePersistedFilters,
        clearFilters: clearPersistedFilters
    } = useFilterPersistence({
        route: '/demonstrativo',
        filterType: 'consultar-demonstrativo',
        defaultValues: {
            competencia: '',
            prestador: '',
            numberPage: 0,
            prestadoresSelecionados: []
        }
    })

    const [loadingDados, setLoadingDados] = useState(false)
    const [pagination, setPagination] = useState<IPagination>()
    const [dataFilterRetornoDemonstrativo, setDataFilterRetornoDemonstrativo] = useState<IDataFilterRetornoDemonstrativo>(() => {
        if (!persistedFilters || Object.keys(persistedFilters).length === 0) {
            return {}
        }
        return persistedFilters
    })
    const [numberPage, setNumberPage] = useState<number>(persistedFilters?.numberPage || 0)

    const updatePage = (newPage: number) => {
        setNumberPage(newPage)
        if (dataFilterRetornoDemonstrativo && Object.keys(dataFilterRetornoDemonstrativo).length > 0) {
            updatePersistedFilters({
                ...dataFilterRetornoDemonstrativo,
                numberPage: newPage,
                prestadoresSelecionados
            })
        }
    }
    const [retornoDemonstrativo, setRetornoDemonstrativo] = useState<IGeDemonstrativo[]>()
    const [prestadoresSelecionados, setPrestadoresSelecionados] = useState<IGeDemonstrativo[]>(persistedFilters?.prestadoresSelecionados || [])
    const [openModalLiberar, setOpenModalLiberar] = useState(false)
    const [openModalCancelar, setOpenModalCancelar] = useState(false)
    const [anchorEl, setAnchorEl] = useState<any | null>(null)
    const [prestadorSelecinado, setPrestadorSelecionado] = useState<IGeDemonstrativo>()
    const [liberarPrestadorModal, setLiberarPrestadorModal] = useState(false)
    const [bloquearNFPrestadorModal, setBloquearNFPrestadorModal] = useState(false)
    const { conditionRender, userProfiles } = useContext(UserContext)

    const open = Boolean(anchorEl)
    const id = open ? 'simple-popover' : undefined

    function loadDados(competencia?: string) {
        setLoadingDados(true)
        const regexCnpj = /^\d{2}\.\d{3}\.\d{3}\/\d{4}-\d{2}$/
        const isCnpj = regexCnpj.test(dataFilterRetornoDemonstrativo?.prestador)
        Demonstrativo?.get({
            competencia: competencia ? competencia : dataFilterRetornoDemonstrativo?.competencia,
            nomeCNPJCodigo: isCnpj ? dataFilterRetornoDemonstrativo?.prestador?.replace(/[^\d]+/g, '') : dataFilterRetornoDemonstrativo?.prestador,
            page: numberPage,
            size: 10
        })
            .then(({ data }) => {
                setLoadingDados(false)
                setRetornoDemonstrativo(data?.content)
                setPagination(PaginationHelper.parserPagination(data, updatePage))
            })
            .catch((err) => {
                addToast({
                    title: err?.response?.data?.message ? err?.response?.data?.message : 'Ocorreu um erro: Desconhecido',
                    type: 'error',
                    duration: 5000
                })
            })
    }

    const titles: { label: string; value: keyof IGeDemonstrativo | 'acao' }[] = [
        { label: 'Nome do prestador', value: 'nomePrestador' },
        { label: 'PED', value: 'numeroPed' },
        { label: 'EMP', value: 'numeroEmp' },
        { label: 'Bloqueio', value: 'bloqueadoAnaliseNF' },
        { label: 'Valor bruto', value: 'valorBruto' },
        { label: '', value: 'acao' }
    ]

    useEffect(() => {
        if (dataFilterRetornoDemonstrativo?.competencia && numberPage !== undefined) {
            loadDados()
        }
    }, [numberPage])

    useEffect(() => {
        if (persistedFilters && persistedFilters.competencia && !retornoDemonstrativo) {
            loadDados()
        }
    }, [persistedFilters])

    useEffect(() => {
        if (dataFilterRetornoDemonstrativo && Object.keys(dataFilterRetornoDemonstrativo).length > 0) {
            updatePersistedFilters({
                ...dataFilterRetornoDemonstrativo,
                numberPage,
                prestadoresSelecionados
            })
        }
    }, [dataFilterRetornoDemonstrativo, prestadoresSelecionados])

    const handleLimparFiltros = () => {
        // Limpar todos os filtros
        const filtrosLimpos = {
            competencia: undefined,
            prestador: undefined
        }

        setDataFilterRetornoDemonstrativo(filtrosLimpos)
        setNumberPage(0)
        setRetornoDemonstrativo([])
        setPrestadoresSelecionados([])

        // Limpar sessionStorage
        try {
            const keys = ['@filters:/demonstrativo:consultar-demonstrativo', '@filters:/demonstrativo:consultar-demonstrativo-visual']
            keys.forEach((key) => sessionStorage.removeItem(key))
        } catch (error) {
            console.error('Erro ao limpar sessionStorage:', error)
        }

        // Limpar filtros persistidos
        clearPersistedFilters()
    }

    return (
        <>
            <FitroRetornoDemonstrativo
                dataFilter={dataFilterRetornoDemonstrativo}
                setDataFilter={setDataFilterRetornoDemonstrativo}
                buscar={(e) => {
                    setNumberPage(0)
                    loadDados(e)
                }}
            />

            {dataFilterRetornoDemonstrativo &&
                Object.keys(dataFilterRetornoDemonstrativo).length > 0 &&
                (dataFilterRetornoDemonstrativo.competencia || dataFilterRetornoDemonstrativo.prestador) && (
                    <div style={{ marginBottom: '16px', marginRight: '20px', textAlign: 'right' }}>
                        <Button variant="outlined" color="secondary" onClick={handleLimparFiltros}>
                            Limpar Filtros
                        </Button>
                    </div>
                )}

            {loadingDados && dataFilterRetornoDemonstrativo?.competencia ? (
                <Box sx={{ display: 'flex', width: '100%', justifyContent: 'center', alignItems: 'center', height: '70vh' }}>
                    <CircularProgress />
                </Box>
            ) : (
                <>
                    {/* <Alert severity="warning" icon={<ReactSVG src="/contas/assets/imgs/mai-ic-schedule.svg" />}>
                        <AlertTitle>Arquivo PED em processamento</AlertTitle>O tempo do processamento varia de acordo com a quantidade de dados
                    </Alert> */}
                    {retornoDemonstrativo?.length > 0 ? (
                        <>
                            <div>
                                <S.ListagemPrestadores>
                                    <p>Demonstrativos Gerados</p>
                                </S.ListagemPrestadores>
                                {retornoDemonstrativo?.length > 0 && (
                                    <TablePagination
                                        titles={titles}
                                        values={retornoDemonstrativo?.map((item) => {
                                            return {
                                                ...item,
                                                nomePrestador: (
                                                    <S.PrestadorInfo>
                                                        <a onClick={() => route.push(`/demonstrativo/demonstrativo-detalhes/${item?.id}`)}>
                                                            {item?.nomePrestador}
                                                        </a>
                                                        <p>{`CNPJ: ${maskCNPJ(item?.cnpj)}, Tipo: ${TipoPrestadorEnum[item?.tipoPrestador]}`}</p>
                                                    </S.PrestadorInfo>
                                                ),
                                                numeroPed: maskArquive(item?.numeroPed),
                                                numeroEmp: maskArquive(item?.numeroEmp),
                                                bloqueadoAnaliseNF: item?.bloqueadoAnaliseNF ? (
                                                    <div style={{ width: 'fit-content' }}>
                                                        <ReactSVG src="/contas/assets/imgs/mai-ic-password.mono-red.svg" />
                                                    </div>
                                                ) : (
                                                    <div style={{ width: 'fit-content' }}>
                                                        <ReactSVG src="/contas/assets/imgs/mai-ic-password.mono.svg" />
                                                    </div>
                                                ),
                                                valorBruto: maskMon(item?.valorBruto.toFixed(2)),
                                                acao: conditionRender(
                                                    (!userProfiles?.profiles?.includes(ProfilesEnum?.CONTAS_A_PAGAR_LEITOR) &&
                                                        userProfiles?.profiles?.includes(ProfilesEnum?.CONTAS_A_PAGAR_EDITOR)) ||
                                                        userProfiles?.profiles?.includes(ProfilesEnum?.OWNER),

                                                    <S.MoreOptions
                                                        onClick={(event) => {
                                                            setAnchorEl(event.currentTarget)
                                                            setPrestadorSelecionado(item)
                                                        }}
                                                        aria-describedby={id}
                                                    >
                                                        <ReactSVG src="/contas/assets/imgs/shape.svg" />
                                                    </S.MoreOptions>
                                                )
                                            }
                                        })}
                                        enableSelect
                                        noSelectAll={true}
                                        handleSelect={setPrestadoresSelecionados}
                                        pagination={pagination}
                                        selectIdField={'id'}
                                        customGridStyles="2.7fr 1.4fr 1.4fr 0.6fr 1fr 0.1fr"
                                    />
                                )}
                            </div>
                            {prestadoresSelecionados?.length > 0 && (
                                <SomaPrestadores
                                    prestadoresSelecionados={prestadoresSelecionados}
                                    // setOpenModalDemonstrativo={setOpenModal}
                                    setOpenModalLiberar={setOpenModalLiberar}
                                    setOpenModalCancelar={setOpenModalCancelar}
                                />
                            )}
                            <LiberarPrestadorModal
                                openModal={liberarPrestadorModal}
                                setOpenModal={setLiberarPrestadorModal}
                                prestadorSelecionado={prestadorSelecinado}
                                competencia={dataFilterRetornoDemonstrativo?.competencia}
                                reloadData={() => loadDados()}
                            />
                            <BloquearNFPrestadorModal
                                openModal={bloquearNFPrestadorModal}
                                setOpenModal={setBloquearNFPrestadorModal}
                                prestadorSelecionado={prestadorSelecinado}
                                competencia={dataFilterRetornoDemonstrativo?.competencia}
                                reloadData={() => loadDados()}
                            />
                            <LiberarDemonstrativoModal
                                openModal={openModalLiberar}
                                setOpenModal={setOpenModalLiberar}
                                prestadoresSelecionados={prestadoresSelecionados}
                                competencia={dataFilterRetornoDemonstrativo?.competencia}
                                reloadData={() => loadDados()}
                            />
                            <CancelarDemonstrativoModal
                                openModal={openModalCancelar}
                                setOpenModal={setOpenModalCancelar}
                                prestadoresSelecionados={prestadoresSelecionados}
                                competencia={dataFilterRetornoDemonstrativo?.competencia}
                                reloadData={() => loadDados()}
                            />
                            <Popover
                                id={id}
                                open={open}
                                anchorEl={anchorEl}
                                onClose={() => setAnchorEl(null)}
                                anchorOrigin={{
                                    vertical: 'bottom',
                                    horizontal: 'left'
                                }}
                            >
                                {!prestadorSelecinado?.liberado && (
                                    <>
                                        <Typography sx={{ p: 2 }}>
                                            <S.PopoverButton
                                                onClick={() => {
                                                    setAnchorEl(null)
                                                    setLiberarPrestadorModal(true)
                                                }}
                                            >
                                                Liberar demonstrativo
                                            </S.PopoverButton>
                                        </Typography>
                                        <HorizontalLine />
                                    </>
                                )}

                                <Typography sx={{ p: 2 }}>
                                    <S.PopoverButton
                                        onClick={() => {
                                            setAnchorEl(null)
                                            setBloquearNFPrestadorModal(true)
                                        }}
                                    >
                                        {prestadorSelecinado?.bloqueadoAnaliseNF ? 'Desbloqueio de análise de NF' : 'Bloqueio de análise de NF'}
                                    </S.PopoverButton>
                                </Typography>
                            </Popover>
                        </>
                    ) : (
                        <NoItensComponent
                            src="/contas/assets/imgs/notfound.svg"
                            titulo="Ops!... nenhum resultado encontrado"
                            subTitulo="Tente outra pesquisa"
                        />
                    )}
                </>
            )}
        </>
    )
}

export default RetornoDemonstrativoComponent

