import { useCallback } from 'react'
import { TipoRelatorioEnum } from 'src/templates/ContasPagarTemplate/RelatoriosTemplate/FitroHistoricoIntegracao/enums'
import { IDataFilterRelatorio } from 'src/templates/ContasPagarTemplate/RelatoriosTemplate/FitroHistoricoIntegracao/filtro-types'

/**
 * Hook para gerenciar campos de filtros de relatórios
 * Determina quais campos devem ser excluídos baseado no tipo de relatório
 */
export const useCleanReportsFields = () => {
    /**
     * Retorna os campos que devem ser excluídos baseado no tipo de relatório
     * @param tipoRelatorio - Tipo do relatório selecionado
     * @returns Array com as chaves dos campos a serem excluídos
     */
    const getCamposParaExcluir = useCallback((tipoRelatorio: TipoRelatorioEnum): (keyof IDataFilterRelatorio)[] => {
        switch (tipoRelatorio) {
            case TipoRelatorioEnum.PRODUCAO_APROVADA:
                return [
                    'situacaoPagamento',
                    'dataInicialDemonstrativo',
                    'dataFinalDemonstrativo',
                    'dataInicialPagamento',
                    'dataFinalPagamento',
                    'dataInicialGeracaoLiq',
                    'dataFinalGeracaoLiq',
                    'codigoIbgeMunicipio',
                    'separarMunicipios',
                    'municipioNome',
                    'prestadoresList'
                ]

            case TipoRelatorioEnum.ACOMPANHAMENTO_PAGAMENTO:
                return [
                    'tipoPrestador',
                    'dataAprovacaoInicial',
                    'dataAprovacaoFinal',
                    'dataInicialPagamento',
                    'dataFinalPagamento',
                    'dataInicialGeracaoLiq',
                    'dataFinalGeracaoLiq',
                    'codigoIbgeMunicipio',
                    'separarMunicipios',
                    'municipioNome',
                    'prestadoresList'
                ]

            case TipoRelatorioEnum.RETENCOES_ISS:
                return [
                    'tipoPrestador',
                    'situacaoPagamento',
                    'dataInicialDemonstrativo',
                    'dataFinalDemonstrativo',
                    'dataInicialGeracaoLiq',
                    'dataFinalGeracaoLiq',
                    'prestadoresList'
                ]

            case TipoRelatorioEnum.LIQUIDACOES:
                return [
                    'tipoPrestador',
                    'situacaoPagamento',
                    'dataInicialDemonstrativo',
                    'dataFinalDemonstrativo',
                    'dataAprovacaoInicial',
                    'dataAprovacaoFinal',
                    'codigoIbgeMunicipio',
                    'separarMunicipios',
                    'municipioNome',
                    'prestadoresList'
                ]

            case TipoRelatorioEnum.EXTRATO_PRODUCAO:
                return [
                    'situacaoPagamento',
                    'dataInicialDemonstrativo',
                    'dataFinalDemonstrativo',
                    'dataInicialPagamento',
                    'dataFinalPagamento',
                    'dataInicialGeracaoLiq',
                    'dataFinalGeracaoLiq',
                    'dataAprovacaoInicial',
                    'dataAprovacaoFinal',
                    'codigoIbgeMunicipio',
                    'separarMunicipios',
                    'municipioNome',
                    'prestadoresList'
                ]

            default:
                return []
        }
    }, [])

    return {
        getCamposParaExcluir
    }
}
