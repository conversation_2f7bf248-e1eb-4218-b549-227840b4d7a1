import styled from 'styled-components'

export const ListagemPrestadores = styled.div`
    display: flex;
    flex-direction: column;
    gap: 24px;
    align-items: flex-start;
    padding: 24px;
    background: #ffffff;
    border-radius: 8px;

    p {
        font-family: 'Open Sans';
        font-style: normal;
        font-weight: 600;
        font-size: 16px;
        line-height: 24px;
        text-transform: capitalize;
        color: #2b45d4;
    }
`
export const Wrapper = styled.div`
    width: 100%;
    background: #ffffff;
    border-radius: 8px;
`

export const AnexoContainer = styled.div`
    display: flex;
    flex-direction: column;
    padding: 24px;
    gap: 24px;
    background: #ffffff;
    border-radius: 8px;

    p {
        font-family: 'Open Sans';
        font-style: normal;
        font-weight: 600;
        font-size: 16px;
        line-height: 24px;
        color: #2b45d4;
    }
`

export const PEDLink = styled.a`
    font-family: 'Open Sans';
    font-style: normal;
    font-weight: 600;
    font-size: 16px;
    line-height: 24px;
    display: flex;
    align-items: center;
    text-decoration-line: underline;
    color: #2b45d4;
    cursor: pointer;
`
export const IconDetailWrapper = styled.div`
    display: flex;
    cursor: pointer;
`

export const ContentItem = styled.div`
    width: 100%;
    display: grid;
    padding: 24px 16px 8px 16px;
    grid-template-columns: 1fr 4fr 1fr 1fr 0.3fr;
`

export const Item = styled.p`
    font-family: 'Open Sans';
    font-style: normal;
    font-weight: 600;
    font-size: 12px;
    line-height: 16px;
    display: flex;
    align-items: center;
    color: #000000;
`
export const ButtonAction = styled.div`
    width: 100%;
    display: flex;
    justify-content: end;
    margin: 32px 0;
`
