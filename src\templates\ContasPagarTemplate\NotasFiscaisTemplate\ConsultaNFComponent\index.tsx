import { Box, Button, CircularProgress, FormControlLabel, Radio, Tab, Tabs } from '@mui/material'
import Badge from 'components/atoms/Badge'
import NoItensComponent from 'components/atoms/NoItensComponent'
import SectionContainer from 'components/atoms/SectionContainer'
import TablePagination from 'components/molecules/TablePagination'
import SomaPrestadores from 'components/organisms/SomaPrestadoresComponent'
import { useRouter } from 'next/router'
import { useContext, useEffect, useState } from 'react'
import { ReactSVG } from 'react-svg'
import { IPagination } from 'src/types/pagination'
import { maskMon } from 'utils/masks'
import { IDataConsultarNF } from '../Filtros/filtro-types'
import FitroConsultaNotasFiscais from '../Filtros/FIltroConsultaNotasFiscais'
import CancelarNFModal from '../ModaisNF/ModalCancelarNF'
import { BadgesBackgroundEnum, BadgesColorEnum, BadgesTextEnum } from './enuns'
import { consultaNFMock } from './mock'
import * as S from './styles'
import { NotaFiscal } from 'src/services/ContasPagar/nota-fiscal-controller'
import { useToast } from 'hooks/toast'
import { INotaFiscalDTO } from 'src/services/ContasPagar/nota-fiscal-controller/contas-type'
import { maskCNPJ } from 'utils/masks/formatCNPJ'
import { parseDateDDMMYYYY } from 'utils/functions'
import { PaginationHelper } from 'utils/pagination-helper'
import FilterNFContext from 'context/FilterNFcontext'
import { ProfilesEnum } from 'src/enum/profiles.enum'
import UserContext from 'context/UserContext/UserContext'

export const initialConsultaNotasFiscaisFilterValue: IDataConsultarNF = {
    ...FitroConsultaNotasFiscais,
    maiorMenorValorBruto: 'ASC'
}

const ConsultaNFComponent = () => {
    const route = useRouter()
    const { addToast } = useToast()
    const [numberPage, setNumberPage] = useState<number>(0)
    const { filterConsultarNF, setFilterConsultaNF } = useContext(FilterNFContext)
    // const [dataFilter, setDataFilter] = useState<IDataConsultarNF>(initialConsultaNotasFiscaisFilterValue)
    const [pagination, setPagination] = useState<IPagination>()
    const [openModal, setOpenModal] = useState(false)
    const [selectedValue, setSelectedValue] = useState<string>()
    // TODO: ADICIONAR TIPAGEM
    const [prestadoresList, setPrestadoresList] = useState<INotaFiscalDTO[]>()
    const [prestadoresSelecionados, setPrestadoresSelecionados] = useState<INotaFiscalDTO[]>()
    const [loadingDados, setLoadingDados] = useState(true)
    const { conditionRender, userProfiles } = useContext(UserContext)

    // const handleChange = (event: React.SyntheticEvent, newValue: string) => {
    //     setValue(newValue)
    // }
    function loadDados(competencia?: string) {
        setLoadingDados(true)
        // const regexCnpj = /^\d{2}\.\d{3}\.\d{3}\/\d{4}-\d{2}$/
        // const isCnpj = regexCnpj.test(dataFilter?.prestador)

        const props = {
            competencia: competencia ? competencia : filterConsultarNF?.competencia,
            // nomeCNPJCodigo: isCnpj ? filterConsultarNF?.prestador?.replace(/[^\d]+/g, '') : filterConsultarNF?.prestador,
            nomeCNPJCodigo: filterConsultarNF?.prestador,
            codigoIbgeMunicipio: filterConsultarNF?.municipio?.value,
            dataEnvioInicial: filterConsultarNF?.dataEnvio?.dataEnvioInicial,
            dataEnvioFinal: filterConsultarNF?.dataEnvio?.dataEnvioFinal,
            page: numberPage,
            sort: filterConsultarNF?.maiorMenorValorBruto ? [`valorNota,${filterConsultarNF?.maiorMenorValorBruto}`] : ['dataEnvio,ASC'],
            size: 10
        }

        NotaFiscal.getAnalisadas({ ...props })
            .then(({ data }) => {
                setPrestadoresList(data.content)
                setLoadingDados(false)
                setPagination(PaginationHelper.parserPagination(data, setNumberPage))
            })
            .catch((err) => {
                addToast({
                    title: err?.response?.data?.message ? err?.response?.data?.message : 'Ocorreu um erro: Desconhecido',
                    type: 'error',
                    duration: 5000
                })
            })
    }

    const titles: { label: string; value: keyof INotaFiscalDTO | 'acao' }[] = [
        { label: 'Nome do prestador', value: 'nomePrestador' },
        { label: 'CNPJ', value: 'cnpj' },
        { label: 'Data Emissão NF', value: 'dataEmissaoNF' },
        { label: 'Data Análise NF', value: 'dataAnalise' },
        { label: 'Número NF', value: 'numeroNF' },
        { label: 'Valor bruto', value: 'valorBruto' },
        { label: 'Situação', value: 'situacao' },
        { label: '', value: 'acao' }
    ]

    const handleLimparFiltros = () => {
        const competenciaAtual = filterConsultarNF?.competencia
        const filtrosLimpos = {
            ...initialConsultaNotasFiscaisFilterValue,
            competencia: competenciaAtual
        }

        setFilterConsultaNF(filtrosLimpos)
        setNumberPage(0)
        setPrestadoresList([])

        // Limpar sessionStorage
        try {
            const keys = ['@filters:/notas-fiscais:consulta-nf', '@filters:/notas-fiscais:filter-consulta-nf']
            keys.forEach((key) => sessionStorage.removeItem(key))
        } catch (error) {
            console.error('Erro ao limpar sessionStorage:', error)
        }

        // Recarregar dados com filtros limpos
        NotaFiscal.getConsulta({
            competencia: competenciaAtual || '',
            nomeCNPJCodigo: '',
            codigoIbgeMunicipio: undefined,
            dataEnvioInicial: undefined,
            dataEnvioFinal: undefined,
            page: 0,
            sort: [`valorNota,${initialConsultaNotasFiscaisFilterValue.maiorMenorValorBruto}`],
            size: 10
        })
            .then(({ data }) => {
                setPrestadoresList(data.content)
                setPagination(PaginationHelper.parserPagination(data, setNumberPage))
            })
            .catch((err) => {
                addToast({
                    title: err?.response?.data?.message ? err?.response?.data?.message : 'Ocorreu um erro: Desconhecido',
                    type: 'error',
                    duration: 5000
                })
            })
    }

    useEffect(() => {
        if (filterConsultarNF?.competencia) {
            loadDados()
        }
    }, [numberPage])

    return (
        <>
            <FitroConsultaNotasFiscais
                dataFilter={filterConsultarNF}
                setDataFilter={setFilterConsultaNF}
                buscar={(e) => {
                    setNumberPage(0)
                    loadDados(e)
                }}
            />

            {filterConsultarNF &&
                Object.keys(filterConsultarNF).length > 0 &&
                (filterConsultarNF.competencia ||
                    filterConsultarNF.prestador ||
                    filterConsultarNF.municipio ||
                    filterConsultarNF.dataEnvio?.dataEnvioInicial ||
                    filterConsultarNF.dataEnvio?.dataEnvioFinal) && (
                    <div style={{ marginBottom: '16px', marginRight: '20px', textAlign: 'right' }}>
                        <Button variant="outlined" color="secondary" onClick={handleLimparFiltros}>
                            Limpar Filtros
                        </Button>
                    </div>
                )}

            {loadingDados ? (
                <Box sx={{ display: 'flex', width: '100%', justifyContent: 'center', alignItems: 'center', height: '70vh' }}>
                    <CircularProgress />
                </Box>
            ) : (
                <S.ConsultaNotasFiscaisContainer>
                    {prestadoresList?.length > 0 ? (
                        <>
                            <div>
                                <S.ListagemPrestadores>
                                    <p>Prestadores</p>
                                </S.ListagemPrestadores>
                                <TablePagination
                                    titles={titles}
                                    values={prestadoresList?.map((item) => {
                                        return {
                                            ...item,
                                            nomePrestador: (
                                                <S.Link onClick={() => route.push(`/notas-fiscais/notas-detalhes/${item?.uuid}`)}>
                                                    {item?.nomePrestador}
                                                </S.Link>
                                            ),
                                            dataEmissaoNF: item?.dataEmissaoNF ? parseDateDDMMYYYY(item?.dataEmissaoNF) : '---',
                                            cnpj: maskCNPJ(item?.cnpj),
                                            dataAnalise: parseDateDDMMYYYY(item?.dataAnalise),

                                            valorBruto: maskMon(item?.valorBruto.toFixed(2)),
                                            situacao: (
                                                <div style={{ width: 'fit-content' }}>
                                                    <Badge
                                                        color={BadgesColorEnum[item?.situacao]}
                                                        background={BadgesBackgroundEnum[item?.situacao]}
                                                        text={BadgesTextEnum[item?.situacao]}
                                                    />
                                                </div>
                                            )
                                            // situacao: item?.situacao === '' && <ReactSVG src="/contas/assets/imgs/mai-ic-warning.mono.svg" />
                                        }
                                    })}
                                    pagination={pagination}
                                    handleSelect={setPrestadoresSelecionados}
                                    customGridStyles="2.5fr 1.5fr 1fr 1fr 1fr 1fr 0.8fr 0.1fr"
                                />
                            </div>
                            {prestadoresSelecionados?.length > 0 && (
                                <SomaPrestadores prestadoresSelecionados={prestadoresSelecionados} setOpenCancelNFModal={setOpenModal} />
                            )}

                            <CancelarNFModal openModal={openModal} setOpenModal={setOpenModal} />

                            {conditionRender(
                                (!userProfiles?.profiles?.includes(ProfilesEnum?.CONTAS_A_PAGAR_LEITOR) &&
                                    userProfiles?.profiles?.includes(ProfilesEnum?.CONTAS_A_PAGAR_EDITOR)) ||
                                    userProfiles?.profiles?.includes(ProfilesEnum?.OWNER),
                                <SectionContainer>
                                    <S.ActionWrapper>
                                        <S.RadioWrapper>
                                            <FormControlLabel
                                                control={
                                                    <Radio
                                                        disabled
                                                        checked={selectedValue === 'PDF'}
                                                        onChange={(e) => setSelectedValue(e.target.value)}
                                                        value="PDF"
                                                        name="radio-buttons"
                                                    />
                                                }
                                                label="PDF"
                                            />
                                            <FormControlLabel
                                                control={
                                                    <Radio
                                                        disabled
                                                        checked={selectedValue === 'Excel'}
                                                        onChange={(e) => setSelectedValue(e.target.value)}
                                                        value="Excel"
                                                        name="radio-buttons"
                                                    />
                                                }
                                                label="Excel"
                                            />
                                        </S.RadioWrapper>

                                        <Button
                                            disabled
                                            color="primary"
                                            startIcon={<ReactSVG src="/contas/assets/imgs/export-icon.svg" />}
                                            variant="text"
                                        >
                                            Exportar listagem
                                        </Button>
                                    </S.ActionWrapper>
                                </SectionContainer>
                            )}
                        </>
                    ) : (
                        <NoItensComponent
                            src="/contas/assets/imgs/notfound.svg"
                            titulo="Ops!... nenhum resultado encontrado"
                            subTitulo="Tente outra pesquisa"
                        />
                    )}
                </S.ConsultaNotasFiscaisContainer>
            )}
        </>
    )
}

export default ConsultaNFComponent
