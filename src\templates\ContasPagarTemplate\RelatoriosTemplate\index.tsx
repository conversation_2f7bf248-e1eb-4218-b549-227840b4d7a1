/* eslint-disable react-hooks/exhaustive-deps */
import { Button } from '@mui/material'
import { useFilterPersistence } from 'hooks/storage/useFilterPersistence'
import { useRouter } from 'next/router'
import { useEffect, useState } from 'react'
import FitroHistoricoIntegracao from './FitroHistoricoIntegracao'
import { IDataFilterRelatorio } from './FitroHistoricoIntegracao/filtro-types'
import { useCleanReportsFields } from 'hooks/relatorios/useCleanReports'
import * as S from './styles'

const initialRelatorioFilterValue: IDataFilterRelatorio = {}

const RelatorioContasPagar = () => {
    const route = useRouter()

    // Hook para persistência dos filtros de relatórios
    const {
        filters: persistedFilters,
        updateFilters: updatePersistedFilters,
        clearFilters: clearPersistedFilters
    } = useFilterPersistence({
        route: '/relatorios',
        filterType: 'relatorios-filtros',
        defaultValues: initialRelatorioFilterValue
    })

    const [dataFilterRelatorio, setDataFilterRelatorio] = useState<IDataFilterRelatorio>(() => {
        if (persistedFilters && Object.keys(persistedFilters).length > 0) {
            return persistedFilters
        }
        return initialRelatorioFilterValue
    })

    const [startSearch, setStartSearch] = useState<boolean>(false)

    // Hook para gerenciar campos de relatórios
    const { getCamposParaExcluir } = useCleanReportsFields()

    useEffect(() => {
        if (dataFilterRelatorio?.tipoRelatorio) {
            const camposParaLimpar = getCamposParaExcluir(dataFilterRelatorio.tipoRelatorio)

            if (camposParaLimpar.length > 0) {
                const temCamposIrrelevantes = camposParaLimpar.some((campo) => dataFilterRelatorio[campo] !== undefined)

                if (temCamposIrrelevantes) {
                    setDataFilterRelatorio((prev) => {
                        const filtrosLimpos = { ...prev }

                        camposParaLimpar.forEach((campo) => {
                            delete filtrosLimpos[campo]
                        })

                        return filtrosLimpos
                    })
                }
            }
        }
    }, [dataFilterRelatorio?.tipoRelatorio])

    const testCases = {
        PRODUCAO_APROVADA: dataFilterRelatorio?.tipoPrestador?.label ? false : true,
        RETENCOES_ISS: false,
        LIQUIDACOES: false,
        ACOMPANHAMENTO_PAGAMENTO: false,
        EXTRATO_PRODUCAO: dataFilterRelatorio?.competencia ? false : true
    }

    const disable = typeof testCases[dataFilterRelatorio?.tipoRelatorio] === 'boolean' ? testCases[dataFilterRelatorio?.tipoRelatorio] : true

    useEffect(() => {
        if (dataFilterRelatorio && Object.keys(dataFilterRelatorio).length > 0) {
            updatePersistedFilters(dataFilterRelatorio)
        }
    }, [dataFilterRelatorio])

    return (
        <>
            {!startSearch && (
                <>
                    <S.Title>Relatórios</S.Title>
                    <S.BoddyContainer>
                        <FitroHistoricoIntegracao
                            dataFilter={dataFilterRelatorio}
                            setDataFilter={setDataFilterRelatorio}
                            buscar={() => {
                                // setNumberPage(0)
                                // loadDados()
                            }}
                        />
                        <S.ContainerButton>
                            <Button
                                color="warning"
                                disabled={disable}
                                onClick={() => {
                                    if (dataFilterRelatorio.tipoRelatorio) {
                                        const { prestadoresList, ...filtrosParaRelatorio } = dataFilterRelatorio

                                        const filtrosParaURL = {
                                            ...filtrosParaRelatorio,
                                            prestadoresIds: prestadoresList?.map((item) => item.value) || []
                                        }

                                        route.push(`/relatorios/exibicao-relatorio?data=${encodeURIComponent(JSON.stringify(filtrosParaURL))}`)
                                    }
                                }}
                            >
                                Gerar Relatório
                            </Button>
                        </S.ContainerButton>
                    </S.BoddyContainer>
                </>
            )}
        </>
    )
}

export default RelatorioContasPagar

