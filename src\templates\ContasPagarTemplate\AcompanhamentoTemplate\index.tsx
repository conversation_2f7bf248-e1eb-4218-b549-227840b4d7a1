import * as S from './styles'
import AcompanhamentoComponents from './Acompanhamento'
import AcompanhamentoComponent from './Acompanhamento'
import HistoricoIntegracao from './HistoricoIntegracao'
import { useState, useEffect } from 'react'
import { useFilterPersistence } from 'hooks/storage/useFilterPersistence'

const AcompanhamentoTemplate = () => {
    // Hook para persistência da aba ativa
    const { filters: persistedTab, updateFilters: updatePersistedTab } = useFilterPersistence({
        route: '/acompanhamento',
        filterType: 'active-tab',
        defaultValues: {
            currentStep: 0
        }
    })

    const [currentStep, setCurrentStep] = useState(() => {
        // Inicializa com o valor persistido ou padrão
        return persistedTab?.currentStep ?? 0
    })

    // Carrega a aba persistida ao inicializar (apenas uma vez)
    useEffect(() => {
        if (persistedTab && persistedTab.currentStep !== undefined) {
            setCurrentStep(persistedTab.currentStep)
        }
    }, [persistedTab])

    const handleTabChange = (step: number) => {
        setCurrentStep(step)
        // Persiste a aba selecionada
        updatePersistedTab({ currentStep: step })
    }

    return (
        <S.BodyContainer>
            <S.CardListOptions>
                <S.Button onClick={() => handleTabChange(0)} actived={currentStep === 0}>
                    <p>Acompanhamento</p>
                </S.Button>
                <S.Button onClick={() => handleTabChange(1)} actived={currentStep === 1}>
                    <p>Histórico da integração</p>
                </S.Button>
            </S.CardListOptions>
            {currentStep === 0 && <AcompanhamentoComponents />}
            {/* {currentStep === 0 && <AcompanhamentoComponent />} */}

            {currentStep === 1 && <HistoricoIntegracao />}
        </S.BodyContainer>
    )
}

export default AcompanhamentoTemplate
