import styled from 'styled-components'

export const ConsultaNotasFiscaisContainer = styled.div`
    display: flex;
    width: 100%;
    flex-direction: column;
    gap: 32px;
`
export const Link = styled.a`
    font-family: 'Open Sans';
    font-style: normal;
    font-weight: 600;
    font-size: 16px;
    line-height: 24px;
    display: flex;
    align-items: center;
    text-decoration-line: underline;
    color: #2b45d4;
    cursor: pointer;
`

export const WrapperFilter = styled.div`
    background: #fff;
    border-radius: 8px;
    padding: 10px;
`

export const ListagemPrestadores = styled.div`
    display: flex;
    flex-direction: column;
    gap: 24px;
    align-items: flex-start;
    padding: 24px;
    background: #ffffff;
    border-radius: 8px 8px 0px 0px;

    p {
        font-family: 'Open Sans';
        font-style: normal;
        font-weight: 600;
        font-size: 16px;
        line-height: 24px;
        text-transform: capitalize;
        color: #2b45d4;
    }
`
export const ActionWrapper = styled.div`
    display: flex;
    gap: 32px;
    width: 100%;
    justify-content: space-between;
`
export const RadioWrapper = styled.div`
    display: flex;
    margin: 0 20px 0 20px;
    gap: 8px;
`
