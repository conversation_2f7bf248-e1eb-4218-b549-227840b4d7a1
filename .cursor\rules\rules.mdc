---
alwaysApply: true
---

# Hooks

UseState sempre deverão ficar no topo do componente (forma padrão ja utilizada no sistema).
UseEffect sempre deve ter uma analise mais precisa para evitar ao máximo loop infinito.

# Hooks Personalizados

Quando a lógica for muito extensa/ou precisar ser reutilizada e houver a necessidade, criar um hook personalizado (src/hooks/nomePasta/useNomeHook.ts)
Quando a lógica for apenas código javascript/typescript reutilizável e houver a necessidade, criar uma função no utils (src/utils/nomePasta/useNomeHook.ts)

# Tipagens

Sempre que terminar de criar um objeto ou qualquer outro campo, verificar a possibilidade de criar uma tipagem, utilizar apenas em casos extremos a tipagem any ou unknown.

